#!/bin/bash

# GCP Cloud Run deployment script for relax-moment podcast platform
# This script builds and deploys the Docker image to Google Cloud Run

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="relax-moment"
TAG="latest"
SERVICE_NAME="relax-moment"
REGION="us-central1"
PLATFORM="managed"
MEMORY="1Gi"
CPU="1"
MAX_INSTANCES="10"
MIN_INSTANCES="0"
PORT="8080"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Function to check if required tools are installed
check_tools() {
    local missing_tools=()
    
    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    fi
    
    if ! command -v gcloud &> /dev/null; then
        missing_tools+=("gcloud")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools:"
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        echo ""
        echo "Please install these tools before running this script."
        exit 1
    fi
}

# Function to check if required environment variables are set
check_env_vars() {
    local missing_vars=()
    
    if [ -z "$GCP_PROJECT_ID" ]; then
        missing_vars+=("GCP_PROJECT_ID")
    fi
    
    if [ -z "$NEXT_PUBLIC_SUPABASE_URL" ]; then
        missing_vars+=("NEXT_PUBLIC_SUPABASE_URL")
    fi
    
    if [ -z "$NEXT_PUBLIC_SUPABASE_ANON_KEY" ]; then
        missing_vars+=("NEXT_PUBLIC_SUPABASE_ANON_KEY")
    fi
    
    if [ -z "$S3_ACCESS_KEY" ]; then
        missing_vars+=("S3_ACCESS_KEY")
    fi
    
    if [ -z "$S3_SECRET_KEY" ]; then
        missing_vars+=("S3_SECRET_KEY")
    fi
    
    if [ -z "$S3_BUCKET_NAME" ]; then
        missing_vars+=("S3_BUCKET_NAME")
    fi
    
    if [ -z "$NEXT_PUBLIC_S3_PUBLIC_URL" ]; then
        missing_vars+=("NEXT_PUBLIC_S3_PUBLIC_URL")
    fi
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        print_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo ""
        echo "Please set these environment variables before running this script."
        exit 1
    fi
}

# Function to configure gcloud
configure_gcloud() {
    print_step "Configuring gcloud..."
    
    # Set project
    gcloud config set project $GCP_PROJECT_ID
    
    # Configure Docker to use gcloud as a credential helper
    gcloud auth configure-docker
    
    print_status "gcloud configured successfully"
}

# Function to build and push Docker image
build_and_push() {
    print_step "Building Docker image..."
    
    local image_url="gcr.io/$GCP_PROJECT_ID/$IMAGE_NAME:$TAG"
    
    # Build the image
    docker build \
        --platform linux/amd64 \
        --build-arg NEXT_PUBLIC_SUPABASE_URL="$NEXT_PUBLIC_SUPABASE_URL" \
        --build-arg NEXT_PUBLIC_SUPABASE_ANON_KEY="$NEXT_PUBLIC_SUPABASE_ANON_KEY" \
        --build-arg S3_ACCESS_KEY="$S3_ACCESS_KEY" \
        --build-arg S3_SECRET_KEY="$S3_SECRET_KEY" \
        --build-arg S3_BUCKET_NAME="$S3_BUCKET_NAME" \
        --build-arg NEXT_PUBLIC_S3_PUBLIC_URL="$NEXT_PUBLIC_S3_PUBLIC_URL" \
        -t $image_url \
        .
    
    if [ $? -eq 0 ]; then
        print_status "Docker image built successfully!"
    else
        print_error "Docker build failed!"
        exit 1
    fi
    
    print_step "Pushing image to Google Container Registry..."
    docker push $image_url
    
    if [ $? -eq 0 ]; then
        print_status "Image pushed successfully!"
        echo "Image URL: $image_url"
    else
        print_error "Docker push failed!"
        exit 1
    fi
    
    echo $image_url
}

# Function to deploy to Cloud Run
deploy_cloudrun() {
    local image_url=$1
    
    print_step "Deploying to Cloud Run..."
    
    gcloud run deploy $SERVICE_NAME \
        --image $image_url \
        --platform $PLATFORM \
        --region $REGION \
        --allow-unauthenticated \
        --memory $MEMORY \
        --cpu $CPU \
        --max-instances $MAX_INSTANCES \
        --min-instances $MIN_INSTANCES \
        --port $PORT \
        --set-env-vars "NODE_ENV=production" \
        --set-env-vars "NEXT_TELEMETRY_DISABLED=1" \
        --set-env-vars "PORT=$PORT"
    
    if [ $? -eq 0 ]; then
        print_status "Deployment successful!"
        
        # Get service URL
        SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --platform=$PLATFORM --region=$REGION --format="value(status.url)")
        
        echo ""
        print_status "Service deployed successfully!"
        print_status "Service URL: $SERVICE_URL"
        print_status "RSS Feed URL: $SERVICE_URL/api/podcast-rss"
        
        echo ""
        print_status "To view logs:"
        echo "  gcloud logs tail --follow --service=$SERVICE_NAME"
        
        echo ""
        print_status "To update the service:"
        echo "  gcloud run services update $SERVICE_NAME --region=$REGION"
        
    else
        print_error "Deployment failed!"
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--project)
            GCP_PROJECT_ID="$2"
            shift 2
            ;;
        -s|--service)
            SERVICE_NAME="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -m|--memory)
            MEMORY="$2"
            shift 2
            ;;
        -c|--cpu)
            CPU="$2"
            shift 2
            ;;
        --max-instances)
            MAX_INSTANCES="$2"
            shift 2
            ;;
        --min-instances)
            MIN_INSTANCES="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -p, --project PROJECT_ID     GCP Project ID"
            echo "  -s, --service SERVICE_NAME   Cloud Run service name (default: relax-moment)"
            echo "  -r, --region REGION          GCP region (default: us-central1)"
            echo "  -m, --memory MEMORY          Memory allocation (default: 1Gi)"
            echo "  -c, --cpu CPU                CPU allocation (default: 1)"
            echo "  --max-instances NUM          Maximum instances (default: 10)"
            echo "  --min-instances NUM          Minimum instances (default: 0)"
            echo "  -h, --help                   Show this help message"
            echo ""
            echo "Required environment variables:"
            echo "  GCP_PROJECT_ID (or use -p flag)"
            echo "  NEXT_PUBLIC_SUPABASE_URL"
            echo "  NEXT_PUBLIC_SUPABASE_ANON_KEY"
            echo "  S3_ACCESS_KEY"
            echo "  S3_SECRET_KEY"
            echo "  S3_BUCKET_NAME"
            echo "  NEXT_PUBLIC_S3_PUBLIC_URL"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use -h or --help for usage information."
            exit 1
            ;;
    esac
done

# Main execution
print_status "Starting Cloud Run deployment process..."

print_step "Checking required tools..."
check_tools

print_step "Checking environment variables..."
check_env_vars

print_status "All checks passed ✓"

configure_gcloud
image_url=$(build_and_push)
deploy_cloudrun $image_url

print_status "Deployment completed successfully! 🚀"
