import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from "@aws-sdk/client-s3"
import { getSignedUrl } from "@aws-sdk/s3-request-presigner"

// Helper function to check if S3 is properly configured
export function isS3Configured(): boolean {
  return !!(
    process.env.S3_ACCESS_KEY &&
    process.env.S3_SECRET_KEY &&
    process.env.S3_BUCKET_NAME &&
    process.env.S3_ACCESS_KEY !== "dummy-access-key" &&
    process.env.S3_SECRET_KEY !== "dummy-secret-key" &&
    process.env.S3_BUCKET_NAME !== "dummy-bucket"
  )
}

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.S3_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.S3_ACCESS_KEY || "dummy-access-key",
    secretAccessKey: process.env.S3_SECRET_KEY || "dummy-secret-key",
  },
})

const bucketName = process.env.S3_BUCKET_NAME || "dummy-bucket"

// Generate a presigned URL for uploading a file
export async function getPresignedUploadUrl(key: string, contentType: string, expiresIn = 3600) {
  const command = new PutObjectCommand({
    Bucket: bucketName,
    Key: key,
    ContentType: contentType,
  })

  try {
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn })
    return signedUrl
  } catch (error) {
    console.error("Error generating presigned URL:", error)
    throw error
  }
}

// Generate a presigned URL for downloading a file
export async function getPresignedDownloadUrl(key: string, expiresIn = 3600) {
  const command = new GetObjectCommand({
    Bucket: bucketName,
    Key: key,
  })

  try {
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn })
    return signedUrl
  } catch (error) {
    console.error("Error generating presigned URL:", error)
    throw error
  }
}

// Delete a file from S3
export async function deleteFile(key: string) {
  const command = new DeleteObjectCommand({
    Bucket: bucketName,
    Key: key,
  })

  try {
    const response = await s3Client.send(command)
    return response
  } catch (error) {
    console.error("Error deleting file:", error)
    throw error
  }
}

// Get the public URL for a file (if using a CDN)
export function getPublicUrl(key: string) {
  const cdnUrl = process.env.CDN_URL
  if (cdnUrl) {
    return `${cdnUrl}/${key}`
  }
  return `https://${bucketName}.s3.amazonaws.com/${key}`
}

// Extract S3 key from a public URL
export function extractKeyFromUrl(url: string): string | null {
  if (!url) return null

  try {
    // Handle different URL formats
    const publicUrl = process.env.NEXT_PUBLIC_S3_PUBLIC_URL

    if (publicUrl && url.startsWith(publicUrl)) {
      // Format: https://domain.com/key
      return url.replace(publicUrl + '/', '')
    }

    // Handle direct S3 URLs
    if (url.includes('.s3.amazonaws.com/')) {
      // Format: https://bucket.s3.amazonaws.com/key
      const parts = url.split('.s3.amazonaws.com/')
      return parts[1] || null
    }

    if (url.includes('.s3.') && url.includes('.amazonaws.com/')) {
      // Format: https://bucket.s3.region.amazonaws.com/key
      const match = url.match(/\.s3\.[^.]+\.amazonaws\.com\/(.+)/)
      return match ? match[1] : null
    }

    // Handle CloudFront or CDN URLs
    const cdnUrl = process.env.CDN_URL
    if (cdnUrl && url.startsWith(cdnUrl)) {
      return url.replace(cdnUrl + '/', '')
    }

    return null
  } catch (error) {
    console.error('Error extracting key from URL:', error)
    return null
  }
}
