#!/bin/bash

# S3 CORS Configuration Script
# This script configures CORS for the S3 bucket to allow file uploads from the web app

BUCKET_NAME="relaxmoment-bill"
REGION="us-west-2"

echo "Setting up CORS configuration for S3 bucket: $BUCKET_NAME"

# Create CORS configuration JSON
cat > cors-config.json << EOF
{
    "CORSRules": [
        {
            "AllowedHeaders": ["*"],
            "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
            "AllowedOrigins": [
                "http://localhost:3000",
                "http://localhost:3001",
                "https://your-domain.com"
            ],
            "ExposeHeaders": ["ETag", "x-amz-meta-custom-header"],
            "MaxAgeSeconds": 3000
        }
    ]
}
EOF

# Apply CORS configuration
echo "Applying CORS configuration..."
aws s3api put-bucket-cors \
    --bucket $BUCKET_NAME \
    --cors-configuration file://cors-config.json \
    --region $REGION

if [ $? -eq 0 ]; then
    echo "✅ CORS configuration applied successfully!"
    echo ""
    echo "Verifying CORS configuration..."
    aws s3api get-bucket-cors --bucket $BUCKET_NAME --region $REGION
else
    echo "❌ Failed to apply CORS configuration"
    echo "Please check your AWS credentials and bucket permissions"
fi

# Clean up
rm cors-config.json

echo ""
echo "📝 Manual Setup Instructions:"
echo "If the script fails, you can manually configure CORS in AWS Console:"
echo "1. Go to https://s3.console.aws.amazon.com/"
echo "2. Click on bucket: $BUCKET_NAME"
echo "3. Go to Permissions tab"
echo "4. Scroll to Cross-origin resource sharing (CORS)"
echo "5. Click Edit and paste the CORS configuration"
