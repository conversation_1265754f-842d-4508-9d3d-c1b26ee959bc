import { NextResponse } from "next/server"
import { supabase, isSupabaseConfigured } from "@/lib/supabase"

export async function GET() {
  try {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ 
        error: "Supabase not configured",
        configured: false 
      }, { status: 503 })
    }

    // Test basic connection
    const { data: connectionTest, error: connectionError } = await supabase
      .from('episodes')
      .select('count', { count: 'exact', head: true })

    if (connectionError) {
      return NextResponse.json({ 
        error: "Database connection failed", 
        details: connectionError.message,
        configured: true,
        connected: false
      }, { status: 500 })
    }

    // Test table structure
    const { data: tables, error: tablesError } = await supabase
      .rpc('get_table_info')
      .select()

    const tableInfo = {
      episodes_count: connectionTest || 0,
      connection_successful: true,
      configured: true,
      connected: true
    }

    return NextResponse.json({
      message: "Database connection successful!",
      ...tableInfo
    })

  } catch (error) {
    console.error("Database test error:", error)
    return NextResponse.json({ 
      error: "Unexpected error during database test",
      details: error instanceof Error ? error.message : "Unknown error",
      configured: isSupabaseConfigured(),
      connected: false
    }, { status: 500 })
  }
}
