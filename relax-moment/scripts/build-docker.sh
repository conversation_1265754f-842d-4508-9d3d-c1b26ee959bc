#!/bin/bash

# Docker build script for relax-moment podcast platform
# This script builds the Docker image with proper environment variables

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="relax-moment"
TAG="latest"
PLATFORM="linux/amd64"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required environment variables are set
check_env_vars() {
    local missing_vars=()
    
    if [ -z "$NEXT_PUBLIC_SUPABASE_URL" ]; then
        missing_vars+=("NEXT_PUBLIC_SUPABASE_URL")
    fi
    
    if [ -z "$NEXT_PUBLIC_SUPABASE_ANON_KEY" ]; then
        missing_vars+=("NEXT_PUBLIC_SUPABASE_ANON_KEY")
    fi
    
    if [ -z "$S3_ACCESS_KEY" ]; then
        missing_vars+=("S3_ACCESS_KEY")
    fi
    
    if [ -z "$S3_SECRET_KEY" ]; then
        missing_vars+=("S3_SECRET_KEY")
    fi
    
    if [ -z "$S3_BUCKET_NAME" ]; then
        missing_vars+=("S3_BUCKET_NAME")
    fi
    
    if [ -z "$NEXT_PUBLIC_S3_PUBLIC_URL" ]; then
        missing_vars+=("NEXT_PUBLIC_S3_PUBLIC_URL")
    fi
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        print_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo ""
        echo "Please set these environment variables before running this script."
        echo "Example:"
        echo "  export NEXT_PUBLIC_SUPABASE_URL='https://your-project.supabase.co'"
        echo "  export NEXT_PUBLIC_SUPABASE_ANON_KEY='your-anon-key'"
        echo "  export S3_ACCESS_KEY='your-access-key'"
        echo "  export S3_SECRET_KEY='your-secret-key'"
        echo "  export S3_BUCKET_NAME='your-bucket-name'"
        echo "  export NEXT_PUBLIC_S3_PUBLIC_URL='https://your-bucket.s3.amazonaws.com'"
        exit 1
    fi
}

# Function to build Docker image
build_image() {
    print_status "Building Docker image: $IMAGE_NAME:$TAG"
    print_status "Platform: $PLATFORM"
    
    docker build \
        --platform $PLATFORM \
        --build-arg NEXT_PUBLIC_SUPABASE_URL="$NEXT_PUBLIC_SUPABASE_URL" \
        --build-arg NEXT_PUBLIC_SUPABASE_ANON_KEY="$NEXT_PUBLIC_SUPABASE_ANON_KEY" \
        --build-arg S3_ACCESS_KEY="$S3_ACCESS_KEY" \
        --build-arg S3_SECRET_KEY="$S3_SECRET_KEY" \
        --build-arg S3_BUCKET_NAME="$S3_BUCKET_NAME" \
        --build-arg NEXT_PUBLIC_S3_PUBLIC_URL="$NEXT_PUBLIC_S3_PUBLIC_URL" \
        -t $IMAGE_NAME:$TAG \
        .
    
    if [ $? -eq 0 ]; then
        print_status "Docker image built successfully!"
        print_status "Image: $IMAGE_NAME:$TAG"
        
        # Show image size
        IMAGE_SIZE=$(docker images $IMAGE_NAME:$TAG --format "table {{.Size}}" | tail -n 1)
        print_status "Image size: $IMAGE_SIZE"
        
        echo ""
        print_status "To run the container locally:"
        echo "  docker run -p 8080:8080 $IMAGE_NAME:$TAG"
        echo ""
        print_status "To tag for GCP Container Registry:"
        echo "  docker tag $IMAGE_NAME:$TAG gcr.io/YOUR_PROJECT_ID/$IMAGE_NAME:$TAG"
        echo ""
        print_status "To push to GCP Container Registry:"
        echo "  docker push gcr.io/YOUR_PROJECT_ID/$IMAGE_NAME:$TAG"
        
    else
        print_error "Docker build failed!"
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -n, --name NAME      Docker image name (default: relax-moment)"
            echo "  -t, --tag TAG        Docker image tag (default: latest)"
            echo "  -p, --platform PLATFORM  Target platform (default: linux/amd64)"
            echo "  -h, --help           Show this help message"
            echo ""
            echo "Required environment variables:"
            echo "  NEXT_PUBLIC_SUPABASE_URL"
            echo "  NEXT_PUBLIC_SUPABASE_ANON_KEY"
            echo "  S3_ACCESS_KEY"
            echo "  S3_SECRET_KEY"
            echo "  S3_BUCKET_NAME"
            echo "  NEXT_PUBLIC_S3_PUBLIC_URL"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use -h or --help for usage information."
            exit 1
            ;;
    esac
done

# Main execution
print_status "Starting Docker build process..."
print_status "Checking environment variables..."
check_env_vars

print_status "Environment variables verified ✓"
build_image
