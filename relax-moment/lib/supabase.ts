import { createClient } from "@supabase/supabase-js"
import type { Episode } from "@/lib/types"

// Create a single supabase client for interacting with your database
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "https://dummy.supabase.co"
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "dummy-key"

// Only create client if we have valid environment variables
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Helper function to check if Supabase is properly configured
export function isSupabaseConfigured(): boolean {
  return !!(
    process.env.NEXT_PUBLIC_SUPABASE_URL &&
    process.env.NEXT_PUBLIC_SUPABASE_URL !== "https://dummy.supabase.co" &&
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY !== "dummy-key"
  )
}

// Episodes API
export async function getEpisodes() {
  const { data, error } = await supabase.from("episodes").select("*").order("publishDate", { ascending: false })

  if (error) {
    console.error("Error fetching episodes:", error)
    return []
  }

  return data as Episode[]
}

export async function getEpisodeById(id: string) {
  const { data, error } = await supabase.from("episodes").select("*").eq("id", id).single()

  if (error) {
    console.error("Error fetching episode:", error)
    return null
  }

  return data as Episode
}

export async function getExpiringEpisodes(daysThreshold = 7) {
  const today = new Date()
  const thresholdDate = new Date()
  thresholdDate.setDate(today.getDate() + daysThreshold)

  const { data, error } = await supabase
    .from("episodes")
    .select("*")
    .lt("expiryDate", thresholdDate.toISOString())
    .gt("expiryDate", today.toISOString())
    .order("expiryDate", { ascending: true })

  if (error) {
    console.error("Error fetching expiring episodes:", error)
    return []
  }

  return data as Episode[]
}

export async function createEpisode(episode: Omit<Episode, "id">) {
  const { data, error } = await supabase.from("episodes").insert([episode]).select()

  if (error) {
    console.error("Error creating episode:", error)
    return null
  }

  return data[0] as Episode
}

export async function updateEpisode(id: string, updates: Partial<Episode>) {
  const { data, error } = await supabase.from("episodes").update(updates).eq("id", id).select()

  if (error) {
    console.error("Error updating episode:", error)
    return null
  }

  return data[0] as Episode
}

export async function deleteEpisode(id: string) {
  const { error } = await supabase.from("episodes").delete().eq("id", id)

  if (error) {
    console.error("Error deleting episode:", error)
    return false
  }

  return true
}
