import { NextResponse } from "next/server"
import { supabase, isSupabaseConfigured } from "@/lib/supabase"

export async function GET() {
  try {
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Database not configured" }, { status: 503 })
    }

    // Get total episodes count
    const { count: totalEpisodes, error: totalError } = await supabase
      .from('episodes')
      .select('*', { count: 'exact', head: true })

    if (totalError) {
      console.error("Error fetching total episodes:", totalError)
      return NextResponse.json({ 
        error: "Failed to fetch stats", 
        details: totalError.message 
      }, { status: 500 })
    }

    // Get published episodes count
    const { count: publishedEpisodes, error: publishedError } = await supabase
      .from('episodes')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'published')

    if (publishedError) {
      console.error("Error fetching published episodes:", publishedError)
      return NextResponse.json({ 
        error: "Failed to fetch stats", 
        details: publishedError.message 
      }, { status: 500 })
    }

    // Get expiring episodes count (expiring in next 7 days)
    const sevenDaysFromNow = new Date()
    sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7)

    const { count: expiringEpisodes, error: expiringError } = await supabase
      .from('episodes')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'published')
      .lt('expiry_date', sevenDaysFromNow.toISOString())
      .gt('expiry_date', new Date().toISOString())

    if (expiringError) {
      console.error("Error fetching expiring episodes:", expiringError)
      return NextResponse.json({ 
        error: "Failed to fetch stats", 
        details: expiringError.message 
      }, { status: 500 })
    }

    // Get active RSS feeds count
    const { count: activeFeeds, error: feedsError } = await supabase
      .from('rss_feeds')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)

    if (feedsError) {
      console.error("Error fetching RSS feeds:", feedsError)
      return NextResponse.json({ 
        error: "Failed to fetch stats", 
        details: feedsError.message 
      }, { status: 500 })
    }

    const stats = {
      totalEpisodes: totalEpisodes || 0,
      publishedEpisodes: publishedEpisodes || 0,
      expiringEpisodes: expiringEpisodes || 0,
      activeFeeds: activeFeeds || 0
    }

    return NextResponse.json({ stats })

  } catch (error) {
    console.error("Stats API error:", error)
    return NextResponse.json({ 
      error: "Unexpected error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
