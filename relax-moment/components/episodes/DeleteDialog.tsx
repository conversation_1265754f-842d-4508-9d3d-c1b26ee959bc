"use client"

import { useState } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Trash, Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface Episode {
  id: string
  title: string
  description: string
  status: string
  publish_date: string
}

interface DeleteDialogProps {
  episode: Episode | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onDeleteSuccess: (episodeId: string) => void
}

export function DeleteDialog({ episode, open, onOpenChange, onDeleteSuccess }: DeleteDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const { toast } = useToast()

  const handleDelete = async () => {
    if (!episode) return

    setIsDeleting(true)

    try {
      const response = await fetch(`/api/episodes/${episode.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete episode')
      }

      toast({
        title: "Episode deleted",
        description: `"${episode.title}" has been permanently deleted.`,
      })

      onDeleteSuccess(episode.id)
      onOpenChange(false)

    } catch (error) {
      console.error('Delete error:', error)
      toast({
        title: "Failed to delete episode",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  if (!episode) return null

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <Trash className="h-5 w-5 text-destructive" />
            Delete Episode
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <p>
              Are you sure you want to delete <strong>"{episode.title}"</strong>?
            </p>
            <p className="text-sm text-muted-foreground">
              This action cannot be undone. The episode and all its data will be permanently removed.
            </p>
            <div className="mt-4 p-3 bg-muted rounded-md">
              <div className="text-sm space-y-1">
                <p><strong>Title:</strong> {episode.title}</p>
                <p><strong>Status:</strong> {episode.status}</p>
                <p><strong>Published:</strong> {new Date(episode.publish_date).toLocaleDateString()}</p>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash className="mr-2 h-4 w-4" />
                Delete Episode
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
