
1. Podcasts management page
    - Upload mp3 files
    - Search feature
    - Edit
    - List
    - Delete
2. Convert wav to mp3
3. Create Podcast RSS feed link and a button to copy the link
4. Store media files in S3


這是我初步的Podcasts app 需求, 目的是能讓我將 Audio files 快速放到網路上, 並提供RSS feed 讓Apple Podcasts app 能使用提供的RSS link 來播放.
因此提供的RSS 必須要遵循Apple Podcasts 的規範.
另外也需要能夠管理這些上傳的 audio files 能容易做到:
Search, Upload, Edit, Delete
Edit 目的是針對audio file 的metadata 進行修改, 包括title, description, image, etc.
audio file的meta data 還需要一個 Expired date 的功能, 每當App 載入時, 會檢查所有audio file 的expired date, 如果超過就刪除.


Tech stack:
React v19 + next.js 15 + Shadcn UI 
S3 
Supabase







