"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Upload, Image as ImageIcon, X, Zap, AlertCircle } from "lucide-react"
import { uploadFile, formatFileSize, type UploadProgress } from "@/lib/upload"
import {
  processImageForApplePodcasts,
  validateImageFile,
  getImageMetadata,
  needsProcessing,
  type ImageProcessingResult
} from "@/lib/client-image-processing"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Image from "next/image"

interface ImageUploaderProps {
  onUploadComplete: (fileUrl: string) => void
  onUploadError: (error: string) => void
  disabled?: boolean
  title?: string
  description?: string
}

export function ImageUploader({
  onUploadComplete,
  onUploadError,
  disabled,
  title = "Cover Image",
  description = "Upload a cover image for your episode (JPG or PNG, max 5MB)"
}: ImageUploaderProps) {
  const [file, setFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [processing, setProcessing] = useState(false)
  const [progress, setProgress] = useState<UploadProgress | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [processingResult, setProcessingResult] = useState<ImageProcessingResult | null>(null)

  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (selectedFile: File) => {
    // Validate file first
    const validation = validateImageFile(selectedFile)
    if (!validation.valid) {
      onUploadError(validation.error || 'Invalid image file')
      return
    }

    setFile(selectedFile)
    setProgress(null)
    setProcessingResult(null)

    // Create preview URL
    const url = URL.createObjectURL(selectedFile)
    setPreviewUrl(url)

    // Check if image needs processing and process it
    try {
      setProcessing(true)
      const metadata = await getImageMetadata(selectedFile)

      if (needsProcessing(metadata.width, metadata.height)) {
        const result = await processImageForApplePodcasts(selectedFile)
        setProcessingResult(result)
        setFile(result.file)

        // Update preview with processed image
        if (previewUrl) {
          URL.revokeObjectURL(previewUrl)
        }
        const processedUrl = URL.createObjectURL(result.file)
        setPreviewUrl(processedUrl)
      }
    } catch (error) {
      console.error('Image processing error:', error)
      onUploadError(`Image processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setProcessing(false)
    }
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      handleFileSelect(selectedFile)
    }
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      handleFileSelect(droppedFile)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  const handleUpload = async () => {
    if (!file) return

    setUploading(true)
    setProgress({ loaded: 0, total: file.size, percentage: 0 })

    try {
      const result = await uploadFile(file, (prog) => {
        setProgress(prog)
      })

      if (result.success && result.fileUrl) {
        onUploadComplete(result.fileUrl)
      } else {
        onUploadError(result.error || 'Upload failed')
      }
    } catch (error) {
      onUploadError(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setUploading(false)
    }
  }

  const handleRemove = () => {
    setFile(null)
    setPreviewUrl(null)
    setProgress(null)
    setProcessingResult(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ImageIcon className="h-5 w-5" />
          {title}
        </CardTitle>
        <CardDescription>
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!file ? (
          <div
            className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors cursor-pointer"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            <ImageIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg font-medium mb-2">Drop your image here</p>
            <p className="text-sm text-muted-foreground mb-4">
              or click to browse files
            </p>
            <p className="text-xs text-muted-foreground mb-4">
              Images will be automatically resized to 1400x1400px for Apple Podcasts compatibility
            </p>
            <Button variant="outline" disabled={disabled}>
              Browse Images
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/jpg,image/png"
              onChange={handleFileChange}
              className="hidden"
              disabled={disabled}
            />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-start gap-4 p-4 border rounded-lg">
              {previewUrl && (
                <div className="relative w-20 h-20 rounded-lg overflow-hidden bg-muted">
                  <Image
                    src={previewUrl}
                    alt="Preview"
                    fill
                    className="object-cover"
                  />
                </div>
              )}
              <div className="flex-1">
                <p className="font-medium">{file.name}</p>
                <p className="text-sm text-muted-foreground">
                  {formatFileSize(file.size)}
                </p>
                {processingResult && (
                  <div className="mt-2 space-y-1">
                    {processingResult.wasProcessed ? (
                      <div className="flex items-center gap-1 text-xs text-green-600">
                        <Zap className="h-3 w-3" />
                        Resized to {processingResult.processedWidth}x{processingResult.processedHeight}px
                      </div>
                    ) : (
                      <div className="flex items-center gap-1 text-xs text-blue-600">
                        <AlertCircle className="h-3 w-3" />
                        Already optimized for Apple Podcasts
                      </div>
                    )}
                  </div>
                )}
              </div>
              <Button
                variant="outline"
                size="icon"
                onClick={handleRemove}
                disabled={uploading || processing}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {processing && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Processing image...</span>
                  <span>Please wait</span>
                </div>
                <Progress value={undefined} className="animate-pulse" />
              </div>
            )}

            {uploading && progress && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Uploading...</span>
                  <span>{progress.percentage}%</span>
                </div>
                <Progress value={progress.percentage} />
              </div>
            )}

            {processingResult && processingResult.wasProcessed && (
              <Alert>
                <Zap className="h-4 w-4" />
                <AlertDescription>
                  Image automatically resized from {processingResult.originalWidth}x{processingResult.originalHeight}px
                  to {processingResult.processedWidth}x{processingResult.processedHeight}px for Apple Podcasts compatibility.
                  File size: {formatFileSize(processingResult.originalSize)} → {formatFileSize(processingResult.processedSize)}
                </AlertDescription>
              </Alert>
            )}

            {!uploading && !processing && (
              <Button onClick={handleUpload} disabled={disabled} className="w-full">
                <Upload className="h-4 w-4 mr-2" />
                Upload Image
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
