import { type NextRequest, NextResponse } from "next/server"
import { supabase, isSupabaseConfigured } from "@/lib/supabase"
import { generateRssFeed } from "@/lib/rss"
import type { Episode, RssFeed } from "@/lib/types"

export async function GET(request: NextRequest) {
  try {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Database not configured" }, { status: 503 })
    }
    // Get feed ID from query params
    const { searchParams } = new URL(request.url)
    const feedId = searchParams.get("id")

    if (!feedId) {
      return NextResponse.json({ error: "Feed ID is required" }, { status: 400 })
    }

    // Get feed data
    const { data: feedData, error: feedError } = await supabase.from("rss_feeds").select("*").eq("id", feedId).single()

    if (feedError || !feedData) {
      return NextResponse.json({ error: "Feed not found" }, { status: 404 })
    }

    // Check if feed is active
    if (!feedData.is_active) {
      return NextResponse.json({ error: "Feed is not active" }, { status: 403 })
    }

    // Get episodes
    const { data: episodesData, error: episodesError } = await supabase
      .from("episodes")
      .select("*")
      .in("status", ["published", "expiring-soon"])
      .order("publish_date", { ascending: false })

    if (episodesError) {
      return NextResponse.json({ error: "Failed to fetch episodes" }, { status: 500 })
    }

    // Generate RSS feed
    const feed = feedData as unknown as RssFeed
    const episodes = episodesData as unknown as Episode[]
    const rssXml = generateRssFeed(feed, episodes)

    // Return RSS feed as XML
    return new NextResponse(rssXml, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=300, s-maxage=600",
      },
    })
  } catch (error) {
    console.error("Error generating RSS feed:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
