# Phase 3: Episodes Management Actions Implementation

## Overview
Implement the missing functionality for the Actions dropdown in the Episodes page (/episodes), including Edit, Download, and Delete operations.

## Current State Analysis

### ✅ What's Already Working
- Episodes list page displays correctly with data from Supabase
- API endpoints exist for all CRUD operations:
  - `GET /api/episodes` - List episodes
  - `POST /api/episodes` - Create episode
  - `GET /api/episodes/[id]` - Get single episode
  - `PATCH /api/episodes/[id]` - Update episode
  - `DELETE /api/episodes/[id]` - Delete episode
- UI components are in place with proper dropdown menu structure

### ❌ What's Missing
- Actions dropdown buttons have no click handlers
- No edit page/modal for episodes
- No download functionality for audio files
- No delete confirmation dialog
- No error handling for failed operations
- No success feedback for completed operations

## Implementation Plan

### Task 1: Create Episode Edit Page
**Estimated Time: 45 minutes**

Create a dedicated edit page at `/episodes/[id]/edit` that reuses upload form components:

1. **Create edit page structure**
   - `app/episodes/[id]/edit/page.tsx`
   - Fetch existing episode data
   - Pre-populate form with current values
   - Handle form submission with PATCH request

2. **Reuse existing components**
   - Extract form components from upload page into reusable components
   - Create shared validation logic
   - Ensure consistent UI/UX between create and edit flows

3. **Add navigation**
   - Update Actions dropdown to link to edit page
   - Add breadcrumb navigation
   - Handle loading and error states

### Task 2: Implement Download Functionality
**Estimated Time: 30 minutes**

Add direct download capability for episode audio files:

1. **Create download handler**
   - Add click handler to download action
   - Generate secure download URLs for S3 files
   - Handle different audio file formats

2. **Add download API endpoint** (if needed)
   - `GET /api/episodes/[id]/download`
   - Generate presigned download URLs
   - Track download analytics (optional)

3. **UI feedback**
   - Show download progress indicator
   - Handle download errors gracefully
   - Provide user feedback on successful downloads

### Task 3: Implement Delete Functionality
**Estimated Time: 25 minutes**

Add episode deletion with proper confirmation:

1. **Create confirmation dialog**
   - Use shadcn/ui AlertDialog component
   - Show episode details in confirmation
   - Prevent accidental deletions

2. **Implement delete handler**
   - Add click handler to delete action
   - Call DELETE API endpoint
   - Update episodes list after successful deletion

3. **Error handling**
   - Handle API errors gracefully
   - Show appropriate error messages
   - Maintain UI state consistency

### Task 4: Enhanced User Experience
**Estimated Time: 20 minutes**

Improve overall UX for episode management:

1. **Loading states**
   - Add loading indicators for all actions
   - Disable buttons during operations
   - Show skeleton loaders where appropriate

2. **Success feedback**
   - Toast notifications for successful operations
   - Optimistic UI updates where possible
   - Clear success messaging

3. **Error handling**
   - Comprehensive error boundaries
   - User-friendly error messages
   - Retry mechanisms for failed operations

## Technical Implementation Details

### File Structure
```
app/
├── episodes/
│   ├── [id]/
│   │   └── edit/
│   │       └── page.tsx          # Edit episode page
│   └── page.tsx                  # Episodes list (update actions)
├── components/
│   ├── episodes/
│   │   ├── EpisodeForm.tsx       # Shared form component
│   │   ├── DeleteDialog.tsx      # Delete confirmation dialog
│   │   └── DownloadButton.tsx    # Download handler component
│   └── ui/                       # Existing shadcn components
└── lib/
    ├── episodes.ts               # Episode-related utilities
    └── validation.ts             # Shared validation (existing)
```

### API Integration
- Reuse existing API endpoints in `/api/episodes/`
- Add error handling and loading states
- Implement optimistic updates where appropriate

### State Management
- Use React state for form management
- Implement proper loading and error states
- Add toast notifications for user feedback

## Testing Strategy

### Manual Testing Checklist
1. **Edit Functionality**
   - [ ] Edit page loads with pre-populated data
   - [ ] Form validation works correctly
   - [ ] Updates save successfully
   - [ ] Navigation works properly

2. **Download Functionality**
   - [ ] Download starts correctly
   - [ ] Different file formats work
   - [ ] Error handling for failed downloads

3. **Delete Functionality**
   - [ ] Confirmation dialog appears
   - [ ] Deletion works correctly
   - [ ] List updates after deletion
   - [ ] Error handling for failed deletions

### Edge Cases to Test
- Network failures during operations
- Invalid episode IDs
- Concurrent modifications
- Large file downloads
- Permission errors

## Success Criteria
- [ ] All three actions (Edit, Download, Delete) work correctly
- [ ] Proper error handling and user feedback
- [ ] Consistent UI/UX across all operations
- [ ] No breaking changes to existing functionality
- [ ] Mobile-responsive design maintained

## Risk Mitigation
- Backup existing episodes data before testing delete functionality
- Implement soft deletes if data recovery is important
- Add rate limiting for download operations
- Validate all user inputs thoroughly

## Next Steps After Phase 3
- Add bulk operations (select multiple episodes)
- Implement episode duplication feature
- Add advanced filtering and sorting options
- Consider implementing episode versioning
