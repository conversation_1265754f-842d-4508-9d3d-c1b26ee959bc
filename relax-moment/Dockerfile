# 1. Builder Stage: Install dependencies and build the standalone output
FROM node:20.19.0-alpine AS builder

# --- Add ARG for build-time env vars ---
ARG NEXT_PUBLIC_SUPABASE_URL
ARG NEXT_PUBLIC_SUPABASE_ANON_KEY
ARG S3_ACCESS_KEY
ARG S3_SECRET_KEY
ARG S3_BUCKET_NAME
ARG NEXT_PUBLIC_S3_PUBLIC_URL

# Set working directory
WORKDIR /app

# Copy only package files first for better caching
COPY package*.json ./
COPY pnpm-lock.yaml* ./

# Install dependencies using npm ci with legacy peer deps for React 19 compatibility
RUN npm ci --legacy-peer-deps

# Copy the rest of the application code
COPY . ./

# Set environment variables for build
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production
ENV S3_REGION=us-west-2
# --- Set ENV from ARG for the build process ---
ENV NEXT_PUBLIC_SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=$NEXT_PUBLIC_SUPABASE_ANON_KEY
ENV S3_ACCESS_KEY=$S3_ACCESS_KEY
ENV S3_SECRET_KEY=$S3_SECRET_KEY
ENV S3_BUCKET_NAME=$S3_BUCKET_NAME
ENV NEXT_PUBLIC_S3_PUBLIC_URL=$NEXT_PUBLIC_S3_PUBLIC_URL

# Build the Next.js app (standalone output)
RUN npm run build

# 2. Runner Stage: Setup the production environment
FROM node:20.19.0-alpine AS runner

WORKDIR /app

# Set environment variables for production
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
# Cloud Run uses PORT environment variable, default to 8080
ENV PORT=8080

# Create a non-root user and group
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy the public folder from builder stage first
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Copy the standalone output from the builder stage
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Switch to the non-root user
USER nextjs

# Expose the port the app listens on (Cloud Run will use PORT env var)
EXPOSE 3000

# Command to run the standalone server
# Note: The standalone output includes a server.js file
CMD ["node", "server.js"]