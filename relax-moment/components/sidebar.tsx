"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { LayoutDashboard, Upload, ListMusic, Rss, Settings, Menu, X } from "lucide-react"
import { useState } from "react"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

const routes = [
  {
    label: "Dashboard",
    icon: LayoutDashboard,
    href: "/",
    color: "text-sky-500",
  },
  {
    label: "Upload",
    icon: Upload,
    href: "/upload",
    color: "text-violet-500",
  },
  {
    label: "Episodes",
    icon: ListMusic,
    href: "/episodes",
    color: "text-pink-700",
  },
  {
    label: "RSS Feeds",
    icon: Rss,
    href: "/rss",
    color: "text-orange-500",
  },
  {
    label: "Settings",
    icon: Settings,
    href: "/settings",
    color: "text-gray-500",
  },
]

export default function Sidebar() {
  const pathname = usePathname()
  const [isMobileOpen, setIsMobileOpen] = useState(false)

  return (
    <>
      {/* Mobile Navigation */}
      <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
        <SheetTrigger asChild className="md:hidden">
          <Button variant="ghost" size="icon" className="absolute left-4 top-4 z-50">
            <Menu />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0">
          <div className="flex h-full flex-col bg-muted/40">
            <div className="px-3 py-4 flex items-center justify-between border-b">
              <h2 className="text-lg font-semibold">Podcasts App</h2>
              <Button variant="ghost" size="icon" onClick={() => setIsMobileOpen(false)}>
                <X className="h-5 w-5" />
              </Button>
            </div>
            <div className="flex-1 overflow-auto py-2">
              <nav className="grid items-start px-2 gap-2">
                {routes.map((route) => (
                  <Link key={route.href} href={route.href} onClick={() => setIsMobileOpen(false)}>
                    <Button
                      variant={pathname === route.href ? "secondary" : "ghost"}
                      className="w-full justify-start gap-2"
                    >
                      <route.icon className={cn("h-5 w-5", route.color)} />
                      {route.label}
                    </Button>
                  </Link>
                ))}
              </nav>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Desktop Navigation */}
      <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
        <div className="flex flex-col flex-grow border-r bg-muted/40">
          <div className="px-4 py-5 flex items-center border-b">
            <h1 className="text-xl font-bold">Podcasts App</h1>
          </div>
          <div className="flex-1 flex flex-col overflow-y-auto pt-5 pb-4">
            <nav className="mt-5 flex-1 px-3 space-y-1">
              {routes.map((route) => (
                <Link key={route.href} href={route.href}>
                  <Button
                    variant={pathname === route.href ? "secondary" : "ghost"}
                    className="w-full justify-start gap-2"
                  >
                    <route.icon className={cn("h-5 w-5", route.color)} />
                    {route.label}
                  </Button>
                </Link>
              ))}
            </nav>
          </div>
        </div>
      </div>
    </>
  )
}
