# Podcasts App 開發計劃

## 專案概述
開發一個 Podcasts 管理應用程式，讓用戶能夠快速將音頻文件發布到網路上，並提供符合 Apple Podcasts 規範的 RSS feed，使 Apple Podcasts app 能夠正常播放內容。

## 核心需求分析

### 1. 主要功能需求

#### 1.1 音頻文件管理系統
- **上傳功能 (Upload)**
  - 支援 MP3 格式文件上傳
  - 支援 WAV 轉 MP3 功能
  - 上傳時自動提取基本 metadata
  - 文件大小和格式驗證

- **搜尋功能 (Search)**
  - 根據標題、描述、標籤進行搜尋
  - 支援模糊搜尋
  - 日期範圍篩選
  - 狀態篩選 (已發布/草稿/過期)

- **編輯功能 (Edit)**
  - 編輯音頻 metadata：
    - 標題 (Title)
    - 描述 (Description)
    - 封面圖片 (Cover Image)
    - 標籤 (Tags)
    - 分類 (Category)
    - 發布日期 (Publish Date)
    - **過期日期 (Expired Date)** - 核心功能
  - 支援批量編輯

- **列表顯示 (List)**
  - 以表格或卡片形式顯示所有音頻文件
  - 支援排序 (日期、標題、大小)
  - 分頁功能
  - 狀態指示器

- **刪除功能 (Delete)**
  - 單個文件刪除
  - 批量刪除
  - 軟刪除 (可恢復)
  - 自動清理過期文件

#### 1.2 過期管理系統
- **自動過期檢查**
  - App 載入時自動檢查所有音頻文件的過期日期
  - 過期文件自動標記或刪除
  - 過期前提醒通知 (可選)

- **過期策略**
  - 立即刪除過期文件
  - 標記為過期但保留一段時間
  - 從 RSS feed 中移除但保留文件

#### 1.3 RSS Feed 生成系統
- **Apple Podcasts 規範相容**
  - 遵循 RSS 2.0 + iTunes 標準
  - 包含必要的 iTunes 標籤
  - 正確的 MIME 類型設定
  - 符合 Apple Podcasts 的技術要求

- **RSS Feed 功能**
  - 動態生成 RSS XML
  - 提供可複製的 RSS 連結
  - 支援 RSS feed 預覽
  - 自動更新 RSS 內容

#### 1.4 媒體儲存系統
- **S3 整合**
  - 音頻文件儲存到 S3
  - 封面圖片儲存到 S3
  - CDN 加速 (可選)
  - 檔案 URL 管理

### 2. 技術需求

#### 2.1 前端技術棧
- React v19
- Next.js 15
- Shadcn UI (現代化界面)
- TypeScript (類型安全)

#### 2.2 後端與資料庫
- Supabase (後端服務)
- PostgreSQL (透過 Supabase)
- Real-time 訂閱 (可選)

#### 2.3 檔案儲存
- AWS S3 (音頻文件和圖片)
- 檔案上傳 API
- 檔案 URL 管理

### 3. 使用者體驗需求

#### 3.1 管理介面
- 直觀的檔案管理介面
- 拖放上傳功能
- 即時預覽功能
- 回應式設計 (支援手機/平板)

#### 3.2 操作流程
1. 使用者上傳音頻文件
2. 編輯 metadata 和設定過期日期
3. 預覽 RSS feed
4. 複製 RSS 連結到 Apple Podcasts
5. 管理和維護已發布的內容

### 4. 非功能性需求

#### 4.1 效能需求
- 快速檔案上傳 (支援大檔案)
- RSS feed 快速生成
- 搜尋響應時間 < 1秒

#### 4.2 安全需求
- 使用者認證和授權
- 檔案上傳安全檢查
- API 速率限制

#### 4.3 可用性需求
- 99.9% 正常運作時間
- RSS feed 持續可用
- 自動備份機制

## 下一步討論重點

1. **技術架構設計**
   - API 設計
   - 資料庫結構
   - 檔案處理流程

2. **實作優先順序**
   - MVP 功能範圍
   - 開發階段劃分
   - 測試策略

3. **部署和維運**
   - 環境配置
   - CI/CD 流程
   - 監控和日誌

---

*此文件為初步需求分析，技術實作細節待進一步討論確定。*
