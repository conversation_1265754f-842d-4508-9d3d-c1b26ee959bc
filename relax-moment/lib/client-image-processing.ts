// Client-side image processing for browser environments
// This handles image resizing using Canvas API

export const APPLE_PODCASTS_IMAGE_SIZE = 1400
export const APPLE_PODCASTS_MAX_SIZE = 3000
export const APPLE_PODCASTS_MIN_SIZE = 1400

export interface ImageProcessingResult {
  file: File
  originalWidth: number
  originalHeight: number
  processedWidth: number
  processedHeight: number
  originalSize: number
  processedSize: number
  wasProcessed: boolean
}

export interface ImageMetadata {
  width: number
  height: number
  size: number
}

/**
 * Get image metadata from file
 */
export function getImageMetadata(file: File): Promise<ImageMetadata> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    const url = URL.createObjectURL(file)
    
    img.onload = () => {
      URL.revokeObjectURL(url)
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
        size: file.size
      })
    }
    
    img.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('Failed to load image'))
    }
    
    img.src = url
  })
}

/**
 * Check if image meets Apple Podcasts requirements
 */
export function meetsApplePodcastsRequirements(width: number, height: number): boolean {
  // Must be square
  if (width !== height) return false
  
  // Must be within size range
  return width >= APPLE_PODCASTS_MIN_SIZE && width <= APPLE_PODCASTS_MAX_SIZE
}

/**
 * Determine if image needs processing
 */
export function needsProcessing(width: number, height: number): boolean {
  return !meetsApplePodcastsRequirements(width, height)
}

/**
 * Resize image using Canvas API
 */
export function resizeImageToCanvas(
  img: HTMLImageElement, 
  targetSize: number,
  maintainAspectRatio: boolean = false
): HTMLCanvasElement {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')!
  
  canvas.width = targetSize
  canvas.height = targetSize
  
  // Fill with white background
  ctx.fillStyle = '#FFFFFF'
  ctx.fillRect(0, 0, targetSize, targetSize)
  
  let sourceX = 0
  let sourceY = 0
  let sourceWidth = img.naturalWidth
  let sourceHeight = img.naturalHeight
  
  if (img.naturalWidth !== img.naturalHeight) {
    if (maintainAspectRatio) {
      // Scale to fit within target size, center on white background
      const scale = Math.min(targetSize / img.naturalWidth, targetSize / img.naturalHeight)
      const scaledWidth = img.naturalWidth * scale
      const scaledHeight = img.naturalHeight * scale
      const x = (targetSize - scaledWidth) / 2
      const y = (targetSize - scaledHeight) / 2
      
      ctx.drawImage(img, x, y, scaledWidth, scaledHeight)
    } else {
      // Crop to square from center
      const size = Math.min(img.naturalWidth, img.naturalHeight)
      sourceX = (img.naturalWidth - size) / 2
      sourceY = (img.naturalHeight - size) / 2
      sourceWidth = size
      sourceHeight = size
      
      ctx.drawImage(img, sourceX, sourceY, sourceWidth, sourceHeight, 0, 0, targetSize, targetSize)
    }
  } else {
    // Already square, just resize
    ctx.drawImage(img, 0, 0, targetSize, targetSize)
  }
  
  return canvas
}

/**
 * Convert canvas to File
 */
export function canvasToFile(canvas: HTMLCanvasElement, filename: string, quality: number = 0.9): Promise<File> {
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) {
        const file = new File([blob], filename, { type: 'image/jpeg' })
        resolve(file)
      }
    }, 'image/jpeg', quality)
  })
}

/**
 * Get processed filename
 */
export function getProcessedFilename(originalFilename: string): string {
  const nameWithoutExt = originalFilename.replace(/\.[^/.]+$/, '')
  return `${nameWithoutExt}.jpg`
}

/**
 * Process image for Apple Podcasts requirements
 */
export async function processImageForApplePodcasts(
  file: File,
  targetSize: number = APPLE_PODCASTS_IMAGE_SIZE,
  maintainAspectRatio: boolean = false,
  quality: number = 0.9
): Promise<ImageProcessingResult> {
  try {
    // Get original metadata
    const originalMetadata = await getImageMetadata(file)
    
    // Check if processing is needed
    if (!needsProcessing(originalMetadata.width, originalMetadata.height)) {
      return {
        file,
        originalWidth: originalMetadata.width,
        originalHeight: originalMetadata.height,
        processedWidth: originalMetadata.width,
        processedHeight: originalMetadata.height,
        originalSize: originalMetadata.size,
        processedSize: originalMetadata.size,
        wasProcessed: false
      }
    }
    
    // Validate target size
    if (targetSize < APPLE_PODCASTS_MIN_SIZE || targetSize > APPLE_PODCASTS_MAX_SIZE) {
      throw new Error(`Target size ${targetSize} is outside Apple Podcasts requirements (${APPLE_PODCASTS_MIN_SIZE}-${APPLE_PODCASTS_MAX_SIZE})`)
    }
    
    // Load image
    const img = await loadImage(file)
    
    // Resize image
    const canvas = resizeImageToCanvas(img, targetSize, maintainAspectRatio)
    
    // Convert to file
    const processedFilename = getProcessedFilename(file.name)
    const processedFile = await canvasToFile(canvas, processedFilename, quality)
    
    return {
      file: processedFile,
      originalWidth: originalMetadata.width,
      originalHeight: originalMetadata.height,
      processedWidth: targetSize,
      processedHeight: targetSize,
      originalSize: originalMetadata.size,
      processedSize: processedFile.size,
      wasProcessed: true
    }
    
  } catch (error) {
    throw new Error(`Image processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Load image from file
 */
function loadImage(file: File): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    const url = URL.createObjectURL(file)
    
    img.onload = () => {
      URL.revokeObjectURL(url)
      resolve(img)
    }
    
    img.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('Failed to load image'))
    }
    
    img.src = url
  })
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // Check file type
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png']
  if (!supportedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Unsupported image format. Please use JPEG or PNG.'
    }
  }
  
  // Check file size (reasonable limit for processing)
  const maxSize = 50 * 1024 * 1024 // 50MB
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'Image file is too large. Maximum size is 50MB.'
    }
  }
  
  return { valid: true }
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
