# S3 CORS 設置指南

## 問題描述
上傳文件時出現 CORS 錯誤：
```
Access to XMLHttpRequest at 'https://relaxmoment-bill.s3.us-west-2.amazonaws.com/...' 
from origin 'http://localhost:3000' has been blocked by CORS policy
```

## 解決方案：設置 S3 CORS

### 方法 1：AWS Console 手動設置 (推薦)

1. **登入 AWS Console**
   - 前往 [AWS S3 Console](https://s3.console.aws.amazon.com/)
   - 使用你的 AWS 帳號登入

2. **找到你的 Bucket**
   - 在 bucket 列表中找到 `relaxmoment-bill`
   - 點擊 bucket 名稱進入詳細頁面

3. **進入 Permissions 設置**
   - 點擊 **Permissions** 標籤
   - 滾動到 **Cross-origin resource sharing (CORS)** 部分

4. **編輯 CORS 配置**
   - 點擊 **Edit** 按鈕
   - 刪除現有內容（如果有的話）
   - 複製並貼上以下 CORS 配置：

```json
[
    {
        "AllowedHeaders": [
            "*"
        ],
        "AllowedMethods": [
            "GET",
            "PUT",
            "POST",
            "DELETE",
            "HEAD"
        ],
        "AllowedOrigins": [
            "http://localhost:3000",
            "http://localhost:3001",
            "https://your-production-domain.com"
        ],
        "ExposeHeaders": [
            "ETag",
            "x-amz-meta-custom-header"
        ],
        "MaxAgeSeconds": 3000
    }
]
```

5. **保存配置**
   - 點擊 **Save changes** 按鈕
   - 等待配置生效（通常幾秒鐘）

### 方法 2：AWS CLI 設置

如果你有 AWS CLI，可以運行：

```bash
# 安裝 AWS CLI (如果尚未安裝)
# macOS: brew install awscli
# 或下載：https://aws.amazon.com/cli/

# 配置 AWS 憑證
aws configure

# 應用 CORS 配置
aws s3api put-bucket-cors \
    --bucket relaxmoment-bill \
    --cors-configuration file://scripts/s3-cors-config.json \
    --region us-west-2

# 驗證配置
aws s3api get-bucket-cors --bucket relaxmoment-bill --region us-west-2
```

### 驗證設置

設置完成後，你可以：

1. **重新測試上傳**
   - 回到 http://localhost:3000/upload
   - 嘗試上傳一個音頻文件
   - 應該不再出現 CORS 錯誤

2. **檢查瀏覽器 Network 標籤**
   - 打開開發者工具 (F12)
   - 切換到 Network 標籤
   - 上傳文件時應該看到成功的 PUT 請求

## CORS 配置說明

- **AllowedOrigins**: 允許的來源域名
  - `http://localhost:3000`: 開發環境
  - 添加你的生產域名
  
- **AllowedMethods**: 允許的 HTTP 方法
  - PUT: 用於文件上傳
  - GET: 用於文件下載
  - DELETE: 用於文件刪除
  
- **AllowedHeaders**: 允許的請求頭
  - `*`: 允許所有頭部（開發時方便）
  
- **MaxAgeSeconds**: 預檢請求的緩存時間

## 常見問題

### Q: 設置後仍然有 CORS 錯誤？
A: 
- 確保 bucket 名稱正確：`relaxmoment-bill`
- 等待幾分鐘讓配置生效
- 清除瀏覽器緩存
- 檢查 AllowedOrigins 是否包含 `http://localhost:3000`

### Q: 生產環境需要修改什麼？
A: 
- 將你的生產域名添加到 AllowedOrigins
- 考慮限制 AllowedHeaders 以提高安全性

### Q: 如何檢查當前的 CORS 配置？
A:
```bash
aws s3api get-bucket-cors --bucket relaxmoment-bill --region us-west-2
```

## 安全注意事項

1. **限制來源域名**: 不要使用 `*` 作為 AllowedOrigins
2. **最小權限原則**: 只允許必要的 HTTP 方法
3. **定期檢查**: 定期檢查和更新 CORS 配置

---

**設置完成後，請重新測試上傳功能！**
