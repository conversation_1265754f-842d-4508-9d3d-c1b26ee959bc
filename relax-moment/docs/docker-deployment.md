# Docker 部署指南

## 概述
本指南說明如何將 relax-moment podcast 平台構建為 Docker 映像並部署到 Google Cloud Run。

## 前置需求

### 必要工具
- Docker Desktop 或 Docker Engine
- Google Cloud SDK (gcloud CLI)
- 有效的 Google Cloud Platform 帳號和項目

### 環境變數
在構建和部署之前，你需要設置以下環境變數：

```bash
# Supabase 配置
export NEXT_PUBLIC_SUPABASE_URL="https://iznqccpmmsgsdbcfwtmb.supabase.co"
export NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"

# AWS S3 配置
export S3_ACCESS_KEY="your-s3-access-key"
export S3_SECRET_KEY="your-s3-secret-key"
export S3_BUCKET_NAME="relaxmoment-bill"
export NEXT_PUBLIC_S3_PUBLIC_URL="https://relaxmoment-bill.s3.amazonaws.com"

# GCP 配置
export GCP_PROJECT_ID="your-gcp-project-id"
```

## Docker 映像構建

### 方法 1：使用構建腳本（推薦）

1. **設置環境變數**：
   ```bash
   # 複製環境變數範例文件
   cp .env.docker.example .env.docker
   
   # 編輯 .env.docker 文件，填入實際值
   nano .env.docker
   
   # 載入環境變數
   source .env.docker
   ```

2. **運行構建腳本**：
   ```bash
   ./scripts/build-docker.sh
   ```

3. **自定義構建選項**：
   ```bash
   # 自定義映像名稱和標籤
   ./scripts/build-docker.sh --name my-podcast --tag v1.0.0
   
   # 查看所有選項
   ./scripts/build-docker.sh --help
   ```

### 方法 2：手動構建

```bash
docker build \
  --platform linux/amd64 \
  --build-arg NEXT_PUBLIC_SUPABASE_URL="$NEXT_PUBLIC_SUPABASE_URL" \
  --build-arg NEXT_PUBLIC_SUPABASE_ANON_KEY="$NEXT_PUBLIC_SUPABASE_ANON_KEY" \
  --build-arg S3_ACCESS_KEY="$S3_ACCESS_KEY" \
  --build-arg S3_SECRET_KEY="$S3_SECRET_KEY" \
  --build-arg S3_BUCKET_NAME="$S3_BUCKET_NAME" \
  --build-arg NEXT_PUBLIC_S3_PUBLIC_URL="$NEXT_PUBLIC_S3_PUBLIC_URL" \
  -t relax-moment:latest \
  .
```

## 本地測試

構建完成後，你可以在本地測試 Docker 映像：

```bash
# 運行容器
docker run -p 8080:8080 relax-moment:latest

# 測試應用
curl http://localhost:8080/api/podcast-rss
```

## Google Cloud Run 部署

### 方法 1：使用部署腳本（推薦）

1. **設置 GCP 項目**：
   ```bash
   # 設置項目 ID
   export GCP_PROJECT_ID="your-gcp-project-id"
   
   # 登入 Google Cloud
   gcloud auth login
   
   # 設置默認項目
   gcloud config set project $GCP_PROJECT_ID
   ```

2. **運行部署腳本**：
   ```bash
   ./scripts/deploy-cloudrun.sh
   ```

3. **自定義部署選項**：
   ```bash
   # 自定義服務名稱和區域
   ./scripts/deploy-cloudrun.sh --service my-podcast --region asia-east1
   
   # 自定義資源配置
   ./scripts/deploy-cloudrun.sh --memory 2Gi --cpu 2 --max-instances 20
   
   # 查看所有選項
   ./scripts/deploy-cloudrun.sh --help
   ```

### 方法 2：手動部署

1. **構建並推送映像**：
   ```bash
   # 標記映像
   docker tag relax-moment:latest gcr.io/$GCP_PROJECT_ID/relax-moment:latest
   
   # 配置 Docker 認證
   gcloud auth configure-docker
   
   # 推送映像
   docker push gcr.io/$GCP_PROJECT_ID/relax-moment:latest
   ```

2. **部署到 Cloud Run**：
   ```bash
   gcloud run deploy relax-moment \
     --image gcr.io/$GCP_PROJECT_ID/relax-moment:latest \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated \
     --memory 1Gi \
     --cpu 1 \
     --max-instances 10 \
     --port 8080
   ```

## 部署後驗證

部署完成後，你會獲得一個 Cloud Run 服務 URL。驗證部署：

```bash
# 獲取服務 URL
SERVICE_URL=$(gcloud run services describe relax-moment --platform=managed --region=us-central1 --format="value(status.url)")

# 測試主頁
curl $SERVICE_URL

# 測試 RSS feed
curl $SERVICE_URL/api/podcast-rss

# 測試 API
curl $SERVICE_URL/api/episodes
```

## 重要 URLs

部署成功後，你的應用將在以下 URLs 可用：

- **主應用**: `https://your-service-url.run.app`
- **RSS Feed**: `https://your-service-url.run.app/api/podcast-rss`
- **Upload 頁面**: `https://your-service-url.run.app/upload`
- **Episodes 管理**: `https://your-service-url.run.app/episodes`

## 監控和日誌

### 查看日誌
```bash
# 實時查看日誌
gcloud logs tail --follow --service=relax-moment

# 查看最近的日誌
gcloud logs read --service=relax-moment --limit=50
```

### 監控指標
在 Google Cloud Console 中：
1. 前往 Cloud Run
2. 選擇你的服務
3. 查看 "Metrics" 標籤

## 故障排除

### 常見問題

1. **構建失敗**：
   - 檢查環境變數是否正確設置
   - 確保 Docker 正在運行
   - 檢查網絡連接

2. **部署失敗**：
   - 檢查 GCP 項目權限
   - 確保 Cloud Run API 已啟用
   - 檢查映像是否成功推送

3. **運行時錯誤**：
   - 檢查 Cloud Run 日誌
   - 驗證環境變數
   - 檢查 Supabase 和 S3 連接

### 調試命令

```bash
# 檢查映像
docker images | grep relax-moment

# 檢查容器
docker ps -a

# 檢查 Cloud Run 服務
gcloud run services list

# 檢查服務詳情
gcloud run services describe relax-moment --region=us-central1
```

## 更新部署

要更新已部署的服務：

1. **更新代碼後重新構建**：
   ```bash
   ./scripts/build-docker.sh --tag v1.1.0
   ```

2. **重新部署**：
   ```bash
   ./scripts/deploy-cloudrun.sh
   ```

或者使用 gcloud 直接更新：

```bash
gcloud run services update relax-moment \
  --image gcr.io/$GCP_PROJECT_ID/relax-moment:v1.1.0 \
  --region us-central1
```

## 安全注意事項

1. **環境變數安全**：
   - 不要在代碼中硬編碼敏感信息
   - 使用 Google Secret Manager 存儲敏感數據

2. **網絡安全**：
   - 考慮設置 Cloud Run 的 ingress 控制
   - 使用 HTTPS（Cloud Run 默認提供）

3. **訪問控制**：
   - 根據需要設置 IAM 權限
   - 考慮是否需要身份驗證

## 成本優化

1. **資源配置**：
   - 根據實際使用調整 CPU 和內存
   - 設置適當的最小/最大實例數

2. **監控使用**：
   - 定期檢查 Cloud Run 使用情況
   - 使用 Google Cloud 計費警報

---

**注意**: 確保在生產環境中使用真實的域名和 SSL 證書，並根據你的具體需求調整配置。
