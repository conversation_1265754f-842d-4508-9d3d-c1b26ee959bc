import { NextRequest, NextResponse } from "next/server"
import { supabase, isSupabaseConfigured } from "@/lib/supabase"
import { deleteFile, extractKeyFromUrl, isS3Configured } from "@/lib/s3"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Database not configured" }, { status: 503 })
    }

    const { data: episode, error } = await supabase
      .from('episodes')
      .select('*')
      .eq('id', params.id)
      .single()

    if (error || !episode) {
      return NextResponse.json({ error: "Episode not found" }, { status: 404 })
    }

    return NextResponse.json({ episode })

  } catch (error) {
    console.error("Get episode error:", error)
    return NextResponse.json({ 
      error: "Unexpected error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Database not configured" }, { status: 503 })
    }

    const body = await request.json()
    const { status, title, description, category, tags, publish_date, expiry_date } = body

    // Build update object with only provided fields
    const updateData: any = {}
    if (status !== undefined) updateData.status = status
    if (title !== undefined) updateData.title = title
    if (description !== undefined) updateData.description = description
    if (category !== undefined) updateData.category = category
    if (tags !== undefined) updateData.tags = tags
    if (publish_date !== undefined) updateData.publish_date = publish_date
    if (expiry_date !== undefined) updateData.expiry_date = expiry_date

    // Add updated timestamp
    updateData.updated_at = new Date().toISOString()

    const { data: episode, error } = await supabase
      .from('episodes')
      .update(updateData)
      .eq('id', params.id)
      .select()
      .single()

    if (error) {
      console.error("Update episode error:", error)
      return NextResponse.json({ 
        error: "Failed to update episode", 
        details: error.message 
      }, { status: 500 })
    }

    if (!episode) {
      return NextResponse.json({ error: "Episode not found" }, { status: 404 })
    }

    return NextResponse.json({ episode })

  } catch (error) {
    console.error("Update episode error:", error)
    return NextResponse.json({ 
      error: "Unexpected error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Database not configured" }, { status: 503 })
    }

    // First, get the episode data to extract file URLs
    const { data: episode, error: fetchError } = await supabase
      .from('episodes')
      .select('audio_url, cover_image_url')
      .eq('id', params.id)
      .single()

    if (fetchError || !episode) {
      return NextResponse.json({ error: "Episode not found" }, { status: 404 })
    }

    // Delete the episode from database first
    const { error: deleteError } = await supabase
      .from('episodes')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error("Delete episode error:", deleteError)
      return NextResponse.json({
        error: "Failed to delete episode",
        details: deleteError.message
      }, { status: 500 })
    }

    // Delete associated files from S3 if S3 is configured
    if (isS3Configured()) {
      const filesToDelete: string[] = []

      // Extract audio file key
      if (episode.audio_url) {
        const audioKey = extractKeyFromUrl(episode.audio_url)
        if (audioKey) {
          filesToDelete.push(audioKey)
        }
      }

      // Extract cover image key
      if (episode.cover_image_url) {
        const imageKey = extractKeyFromUrl(episode.cover_image_url)
        if (imageKey) {
          filesToDelete.push(imageKey)
        }
      }

      // Delete files from S3 (don't fail the request if S3 deletion fails)
      for (const key of filesToDelete) {
        try {
          await deleteFile(key)
          console.log(`Successfully deleted S3 file: ${key}`)
        } catch (s3Error) {
          console.error(`Failed to delete S3 file ${key}:`, s3Error)
          // Continue with other files even if one fails
        }
      }
    }

    return NextResponse.json({
      message: "Episode deleted successfully",
      filesDeleted: isS3Configured() ? "S3 files deletion attempted" : "S3 not configured"
    })

  } catch (error) {
    console.error("Delete episode error:", error)
    return NextResponse.json({
      error: "Unexpected error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
