export interface Episode {
  id: string
  title: string
  description: string
  audioUrl: string
  coverImageUrl: string
  publishDate: string
  expiryDate: string
  duration: string
  status: "draft" | "published" | "expiring-soon" | "expired"
  category: string
  tags: string[]
}

export interface RssFeed {
  id: string
  title: string
  description: string
  author: string
  email: string
  language: string
  copyright: string
  imageUrl: string
  category: string
  subcategory: string
  explicit: boolean
  url: string
  isActive: boolean
  isPrivate: boolean
}

export interface AppSettings {
  general: {
    podcastName: string
    podcastDescription: string
    author: string
    email: string
    website: string
  }
  storage: {
    s3Bucket: string
    s3Region: string
    accessKeyId: string
    secretAccessKey: string
    cdnUrl: string
  }
  expiration: {
    autoExpire: boolean
    expirationAction: "mark" | "remove-rss" | "archive" | "delete"
    notificationDays: number
    retentionPeriod: number | "forever"
  }
  rss: {
    itunesCategory: string
    itunesSubcategory: string
    explicitContent: boolean
    language: string
    completeSeries: boolean
    blockFromDirectory: boolean
  }
}
