import { NextResponse } from "next/server"
import { supabase, isSupabaseConfigured } from "@/lib/supabase"

export async function GET() {
  try {
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Database not configured" }, { status: 503 })
    }

    const { data: episodes, error } = await supabase
      .from('episodes')
      .select('*')
      .order('publish_date', { ascending: false })

    if (error) {
      console.error("Error fetching episodes:", error)
      return NextResponse.json({ 
        error: "Failed to fetch episodes", 
        details: error.message 
      }, { status: 500 })
    }

    return NextResponse.json({ episodes })

  } catch (error) {
    console.error("Episodes API error:", error)
    return NextResponse.json({ 
      error: "Unexpected error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Database not configured" }, { status: 503 })
    }

    const body = await request.json()
    const { title, description, audio_url, duration, category, cover_image_url, tags, publish_date, expiry_date } = body

    if (!title || !audio_url) {
      return NextResponse.json({ 
        error: "Title and audio_url are required" 
      }, { status: 400 })
    }

    const episodeData = {
      title,
      description,
      audio_url,
      duration,
      category,
      cover_image_url,
      tags: tags || [],
      publish_date: publish_date || new Date().toISOString(),
      expiry_date,
      status: 'draft'
    }

    const { data: episode, error } = await supabase
      .from('episodes')
      .insert([episodeData])
      .select()
      .single()

    if (error) {
      console.error("Error creating episode:", error)
      return NextResponse.json({ 
        error: "Failed to create episode", 
        details: error.message 
      }, { status: 500 })
    }

    return NextResponse.json({ episode }, { status: 201 })

  } catch (error) {
    console.error("Create episode error:", error)
    return NextResponse.json({ 
      error: "Unexpected error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
