"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"

export default function SettingsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const handleSaveSettings = () => {
    setIsLoading(true)

    // Simulate saving settings
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Settings saved",
        description: "Your settings have been saved successfully.",
      })
    }, 1500)
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">Manage your podcast app settings</p>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="storage">Storage</TabsTrigger>
          <TabsTrigger value="expiration">Expiration</TabsTrigger>
          <TabsTrigger value="rss">RSS</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Configure your podcast app general settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="podcast-name">Podcast Name</Label>
                <Input id="podcast-name" defaultValue="Your Podcast Name" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="podcast-description">Podcast Description</Label>
                <Textarea
                  id="podcast-description"
                  defaultValue="Your podcast description goes here."
                  className="min-h-[100px]"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="podcast-author">Author</Label>
                <Input id="podcast-author" defaultValue="Your Name" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="podcast-email">Contact Email</Label>
                <Input id="podcast-email" type="email" defaultValue="<EMAIL>" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="podcast-website">Website</Label>
                <Input id="podcast-website" type="url" defaultValue="https://yourwebsite.com" />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings} disabled={isLoading} className="ml-auto">
                {isLoading ? "Saving..." : "Save Settings"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="storage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Storage Settings</CardTitle>
              <CardDescription>Configure your S3 storage settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="s3-bucket">S3 Bucket Name</Label>
                <Input id="s3-bucket" defaultValue="your-podcast-bucket" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="s3-region">S3 Region</Label>
                <Select defaultValue="us-east-1">
                  <SelectTrigger id="s3-region">
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="us-east-1">US East (N. Virginia)</SelectItem>
                    <SelectItem value="us-east-2">US East (Ohio)</SelectItem>
                    <SelectItem value="us-west-1">US West (N. California)</SelectItem>
                    <SelectItem value="us-west-2">US West (Oregon)</SelectItem>
                    <SelectItem value="ap-east-1">Asia Pacific (Hong Kong)</SelectItem>
                    <SelectItem value="ap-southeast-1">Asia Pacific (Singapore)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="s3-access-key">Access Key ID</Label>
                <Input id="s3-access-key" type="password" defaultValue="••••••••••••••••" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="s3-secret-key">Secret Access Key</Label>
                <Input id="s3-secret-key" type="password" defaultValue="••••••••••••••••••••••••••••••••" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="cdn-url">CDN URL (Optional)</Label>
                <Input id="cdn-url" placeholder="https://cdn.yourwebsite.com" />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="mr-auto bg-transparent">
                Test Connection
              </Button>
              <Button onClick={handleSaveSettings} disabled={isLoading}>
                {isLoading ? "Saving..." : "Save Settings"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="expiration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Expiration Settings</CardTitle>
              <CardDescription>Configure how expired episodes are handled</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-expire">Auto Expire Episodes</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically handle episodes when they reach their expiration date
                  </p>
                </div>
                <Switch id="auto-expire" defaultChecked />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="expiration-action">Expiration Action</Label>
                <Select defaultValue="mark">
                  <SelectTrigger id="expiration-action">
                    <SelectValue placeholder="Select action" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mark">Mark as Expired</SelectItem>
                    <SelectItem value="remove-rss">Remove from RSS Feed Only</SelectItem>
                    <SelectItem value="archive">Archive Episode</SelectItem>
                    <SelectItem value="delete">Delete Episode</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="notification-days">Expiration Notification</Label>
                <Select defaultValue="7">
                  <SelectTrigger id="notification-days">
                    <SelectValue placeholder="Select days" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 day before</SelectItem>
                    <SelectItem value="3">3 days before</SelectItem>
                    <SelectItem value="7">7 days before</SelectItem>
                    <SelectItem value="14">14 days before</SelectItem>
                    <SelectItem value="30">30 days before</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="retention-period">Retention Period</Label>
                <Select defaultValue="30">
                  <SelectTrigger id="retention-period">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">7 days</SelectItem>
                    <SelectItem value="14">14 days</SelectItem>
                    <SelectItem value="30">30 days</SelectItem>
                    <SelectItem value="60">60 days</SelectItem>
                    <SelectItem value="90">90 days</SelectItem>
                    <SelectItem value="forever">Keep Forever</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  How long to keep expired episodes before permanent deletion
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings} disabled={isLoading} className="ml-auto">
                {isLoading ? "Saving..." : "Save Settings"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="rss" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>RSS Feed Settings</CardTitle>
              <CardDescription>Configure your RSS feed settings for Apple Podcasts compatibility</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="itunes-category">iTunes Category</Label>
                <Select defaultValue="technology">
                  <SelectTrigger id="itunes-category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="arts">Arts</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="comedy">Comedy</SelectItem>
                    <SelectItem value="education">Education</SelectItem>
                    <SelectItem value="fiction">Fiction</SelectItem>
                    <SelectItem value="health">Health & Fitness</SelectItem>
                    <SelectItem value="music">Music</SelectItem>
                    <SelectItem value="news">News</SelectItem>
                    <SelectItem value="science">Science</SelectItem>
                    <SelectItem value="society">Society & Culture</SelectItem>
                    <SelectItem value="sports">Sports</SelectItem>
                    <SelectItem value="technology">Technology</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="itunes-subcategory">iTunes Subcategory</Label>
                <Select defaultValue="tech-news">
                  <SelectTrigger id="itunes-subcategory">
                    <SelectValue placeholder="Select subcategory" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tech-news">Tech News</SelectItem>
                    <SelectItem value="software">Software How-To</SelectItem>
                    <SelectItem value="podcasting">Podcasting</SelectItem>
                    <SelectItem value="gadgets">Gadgets</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="explicit-content">Explicit Content</Label>
                <Select defaultValue="no">
                  <SelectTrigger id="explicit-content">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no">No</SelectItem>
                    <SelectItem value="yes">Yes</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="language">Language</Label>
                <Select defaultValue="en-us">
                  <SelectTrigger id="language">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en-us">English (US)</SelectItem>
                    <SelectItem value="en-gb">English (UK)</SelectItem>
                    <SelectItem value="zh-tw">Chinese (Traditional)</SelectItem>
                    <SelectItem value="zh-cn">Chinese (Simplified)</SelectItem>
                    <SelectItem value="ja">Japanese</SelectItem>
                    <SelectItem value="ko">Korean</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="complete-series">Complete Series</Label>
                  <p className="text-sm text-muted-foreground">Mark this podcast as a completed series</p>
                </div>
                <Switch id="complete-series" />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="block-directory">Block from Directory</Label>
                  <p className="text-sm text-muted-foreground">Prevent this podcast from appearing in Apple Podcasts</p>
                </div>
                <Switch id="block-directory" />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings} disabled={isLoading} className="ml-auto">
                {isLoading ? "Saving..." : "Save Settings"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
