"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Copy, ExternalLink, Plus, RefreshCw } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import Link from "next/link"

export default function RSSPage() {
  const [rssUrl, setRssUrl] = useState("https://example.com/podcast-feed.xml")
  const [isRefreshing, setIsRefreshing] = useState(false)
  const { toast } = useToast()

  const handleCopyRssUrl = () => {
    navigator.clipboard.writeText(rssUrl)
    toast({
      title: "RSS URL copied",
      description: "The RSS feed URL has been copied to your clipboard.",
    })
  }

  const handleRefreshFeed = () => {
    setIsRefreshing(true)

    // Simulate refresh process
    setTimeout(() => {
      setIsRefreshing(false)
      toast({
        title: "RSS feed refreshed",
        description: "Your RSS feed has been refreshed successfully.",
      })
    }, 1500)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">RSS Feeds</h1>
          <p className="text-muted-foreground">Manage your podcast RSS feeds</p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          New RSS Feed
        </Button>
      </div>

      <Tabs defaultValue="main-feed" className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="main-feed">Main Feed</TabsTrigger>
            <TabsTrigger value="premium-feed">Premium Feed</TabsTrigger>
          </TabsList>
          <Button variant="outline" size="sm" onClick={handleRefreshFeed} disabled={isRefreshing}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
            {isRefreshing ? "Refreshing..." : "Refresh Feed"}
          </Button>
        </div>

        <TabsContent value="main-feed" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Main Podcast Feed</CardTitle>
                  <CardDescription>
                    Your primary podcast RSS feed for Apple Podcasts and other platforms
                  </CardDescription>
                </div>
                <Badge>Active</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="rss-url">RSS Feed URL</Label>
                  <div className="flex">
                    <Input
                      id="rss-url"
                      value={rssUrl}
                      onChange={(e) => setRssUrl(e.target.value)}
                      className="rounded-r-none"
                    />
                    <Button variant="secondary" className="rounded-l-none" onClick={handleCopyRssUrl}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Submit this URL to Apple Podcasts and other podcast directories
                  </p>
                </div>

                <div className="grid gap-2">
                  <Label>Feed Preview</Label>
                  <div className="bg-muted rounded-md p-4 overflow-auto max-h-[300px]">
                    <pre className="text-xs">
                      {`<?xml version="1.0" encoding="UTF-8"?>
<rss xmlns:itunes="http://www.itunes.com/dtds/podcast-1.0.dtd" version="2.0">
  <channel>
    <title>Your Podcast Name</title>
    <link>https://yourwebsite.com</link>
    <language>en-us</language>
    <copyright>© 2025 Your Name</copyright>
    <itunes:author>Your Name</itunes:author>
    <description>
      Your podcast description goes here.
    </description>
    <itunes:image href="https://example.com/podcast-cover.jpg"/>
    <itunes:category text="Technology"/>
    
    <item>
      <title>The Future of AI in Content Creation</title>
      <itunes:author>Your Name</itunes:author>
      <description>
        In this episode, we discuss the future of AI in content creation.
      </description>
      <enclosure url="https://example.com/episodes/episode1.mp3" 
                length="34216300" type="audio/mpeg"/>
      <pubDate>Tue, 25 Jun 2025 12:00:00 GMT</pubDate>
      <itunes:duration>45:22</itunes:duration>
      <guid>https://example.com/episodes/episode1</guid>
    </item>
    
    <item>
      <title>Interview with Tech Industry Leader</title>
      <itunes:author>Your Name</itunes:author>
      <description>
        An exclusive interview with a tech industry leader.
      </description>
      <enclosure url="https://example.com/episodes/episode2.mp3" 
                length="28900000" type="audio/mpeg"/>
      <pubDate>Thu, 20 Jun 2025 12:00:00 GMT</pubDate>
      <itunes:duration>32:15</itunes:duration>
      <guid>https://example.com/episodes/episode2</guid>
    </item>
    
    <!-- More episodes... -->
    
  </channel>
</rss>`}
                    </pre>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">
                <Link href="https://podcastsconnect.apple.com/" target="_blank" className="flex items-center">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Submit to Apple Podcasts
                </Link>
              </Button>
              <Button>Edit Feed Settings</Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Feed Settings</CardTitle>
              <CardDescription>Configure your podcast feed settings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="podcast-title">Podcast Title</Label>
                  <Input id="podcast-title" defaultValue="Your Podcast Name" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="podcast-description">Podcast Description</Label>
                  <Textarea
                    id="podcast-description"
                    defaultValue="Your podcast description goes here."
                    className="min-h-[100px]"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="podcast-author">Author</Label>
                  <Input id="podcast-author" defaultValue="Your Name" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="podcast-language">Language</Label>
                  <Input id="podcast-language" defaultValue="en-us" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="podcast-copyright">Copyright</Label>
                  <Input id="podcast-copyright" defaultValue="© 2025 Your Name" />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="ml-auto">Save Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="premium-feed" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Premium Podcast Feed</CardTitle>
                  <CardDescription>Private RSS feed for premium subscribers</CardDescription>
                </div>
                <Badge variant="outline">Inactive</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold">Premium Feed Not Configured</h3>
                  <p className="text-muted-foreground">
                    Set up a premium feed to offer exclusive content to your subscribers
                  </p>
                  <Button className="mt-4">Configure Premium Feed</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
