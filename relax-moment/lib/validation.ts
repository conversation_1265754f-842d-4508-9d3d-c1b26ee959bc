// Form validation utilities

export interface EpisodeFormData {
  title: string
  description: string
  category: string
  tags: string[]
  publishDate: Date
  expiryDate?: Date
  status: 'draft' | 'published'
  audioFile?: File
  coverImage?: File
  audioUrl?: string
  coverImageUrl?: string
}

export interface ValidationError {
  field: string
  message: string
}

export interface ValidationResult {
  valid: boolean
  errors: ValidationError[]
}

// Validation rules
const VALIDATION_RULES = {
  title: {
    minLength: 3,
    maxLength: 200,
    required: true,
  },
  description: {
    minLength: 10,
    maxLength: 2000,
    required: true,
  },
  category: {
    required: true,
    allowedValues: ['technology', 'business', 'education', 'health', 'entertainment', 'news'],
  },
  tags: {
    maxCount: 10,
    maxLength: 50, // per tag
  },
  publishDate: {
    required: true,
  },
  status: {
    required: true,
    allowedValues: ['draft', 'published'],
  },
}

export function validateTitle(title: string): ValidationError | null {
  if (!title || title.trim().length === 0) {
    return { field: 'title', message: 'Title is required' }
  }
  
  if (title.length < VALIDATION_RULES.title.minLength) {
    return { 
      field: 'title', 
      message: `Title must be at least ${VALIDATION_RULES.title.minLength} characters` 
    }
  }
  
  if (title.length > VALIDATION_RULES.title.maxLength) {
    return { 
      field: 'title', 
      message: `Title must be less than ${VALIDATION_RULES.title.maxLength} characters` 
    }
  }
  
  return null
}

export function validateDescription(description: string): ValidationError | null {
  if (!description || description.trim().length === 0) {
    return { field: 'description', message: 'Description is required' }
  }
  
  if (description.length < VALIDATION_RULES.description.minLength) {
    return { 
      field: 'description', 
      message: `Description must be at least ${VALIDATION_RULES.description.minLength} characters` 
    }
  }
  
  if (description.length > VALIDATION_RULES.description.maxLength) {
    return { 
      field: 'description', 
      message: `Description must be less than ${VALIDATION_RULES.description.maxLength} characters` 
    }
  }
  
  return null
}

export function validateCategory(category: string): ValidationError | null {
  if (!category || category.trim().length === 0) {
    return { field: 'category', message: 'Category is required' }
  }
  
  if (!VALIDATION_RULES.category.allowedValues.includes(category)) {
    return { 
      field: 'category', 
      message: 'Please select a valid category' 
    }
  }
  
  return null
}

export function validateTags(tags: string[]): ValidationError | null {
  if (tags.length > VALIDATION_RULES.tags.maxCount) {
    return { 
      field: 'tags', 
      message: `Maximum ${VALIDATION_RULES.tags.maxCount} tags allowed` 
    }
  }
  
  for (const tag of tags) {
    if (tag.length > VALIDATION_RULES.tags.maxLength) {
      return { 
        field: 'tags', 
        message: `Each tag must be less than ${VALIDATION_RULES.tags.maxLength} characters` 
      }
    }
  }
  
  return null
}

export function validatePublishDate(publishDate: Date): ValidationError | null {
  if (!publishDate) {
    return { field: 'publishDate', message: 'Publish date is required' }
  }
  
  // Allow past dates for flexibility
  return null
}

export function validateExpiryDate(expiryDate: Date | undefined, publishDate: Date): ValidationError | null {
  if (!expiryDate) {
    return null // Optional field
  }
  
  if (expiryDate <= publishDate) {
    return { 
      field: 'expiryDate', 
      message: 'Expiry date must be after publish date' 
    }
  }
  
  return null
}

export function validateStatus(status: string): ValidationError | null {
  if (!status) {
    return { field: 'status', message: 'Status is required' }
  }
  
  if (!VALIDATION_RULES.status.allowedValues.includes(status)) {
    return { 
      field: 'status', 
      message: 'Please select a valid status' 
    }
  }
  
  return null
}

export function validateAudioFile(audioFile: File | undefined, audioUrl: string | undefined): ValidationError | null {
  if (!audioFile && !audioUrl) {
    return { field: 'audioFile', message: 'Audio file is required' }
  }
  
  return null
}

// Main validation function
export function validateEpisodeForm(data: EpisodeFormData): ValidationResult {
  const errors: ValidationError[] = []
  
  // Validate each field
  const titleError = validateTitle(data.title)
  if (titleError) errors.push(titleError)
  
  const descriptionError = validateDescription(data.description)
  if (descriptionError) errors.push(descriptionError)
  
  const categoryError = validateCategory(data.category)
  if (categoryError) errors.push(categoryError)
  
  const tagsError = validateTags(data.tags)
  if (tagsError) errors.push(tagsError)
  
  const publishDateError = validatePublishDate(data.publishDate)
  if (publishDateError) errors.push(publishDateError)
  
  const expiryDateError = validateExpiryDate(data.expiryDate, data.publishDate)
  if (expiryDateError) errors.push(expiryDateError)
  
  const statusError = validateStatus(data.status)
  if (statusError) errors.push(statusError)
  
  const audioFileError = validateAudioFile(data.audioFile, data.audioUrl)
  if (audioFileError) errors.push(audioFileError)
  
  return {
    valid: errors.length === 0,
    errors,
  }
}

// Helper function to get error message for a specific field
export function getFieldError(errors: ValidationError[], field: string): string | undefined {
  const error = errors.find(e => e.field === field)
  return error?.message
}

// Helper function to check if a field has an error
export function hasFieldError(errors: ValidationError[], field: string): boolean {
  return errors.some(e => e.field === field)
}

// Clean and format tags
export function formatTags(tagsString: string): string[] {
  return tagsString
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0)
    .slice(0, VALIDATION_RULES.tags.maxCount)
}

// Convert tags array to string for display
export function tagsToString(tags: string[]): string {
  return tags.join(', ')
}
