import { NextRequest, NextResponse } from "next/server"
import { getPresignedUploadUrl, getPresignedDownloadUrl, deleteFile, isS3Configured } from "@/lib/s3"

export async function GET(request: NextRequest) {
  try {
    if (!isS3Configured()) {
      return NextResponse.json({ 
        error: "S3 not configured",
        configured: false 
      }, { status: 503 })
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'info'

    switch (action) {
      case 'info':
        return NextResponse.json({
          message: "S3 configuration successful!",
          configured: true,
          bucket: process.env.S3_BUCKET_NAME,
          region: process.env.S3_REGION,
          publicUrl: process.env.NEXT_PUBLIC_S3_PUBLIC_URL
        })

      case 'upload-url':
        const fileName = searchParams.get('fileName') || 'test-file.txt'
        const contentType = searchParams.get('contentType') || 'text/plain'
        const key = `test/${Date.now()}-${fileName}`
        
        const uploadUrl = await getPresignedUploadUrl(key, contentType, 3600)
        
        return NextResponse.json({
          message: "Upload URL generated successfully",
          uploadUrl,
          key,
          publicUrl: `${process.env.NEXT_PUBLIC_S3_PUBLIC_URL}/${key}`
        })

      case 'download-url':
        const downloadKey = searchParams.get('key')
        if (!downloadKey) {
          return NextResponse.json({ error: "Key parameter required" }, { status: 400 })
        }
        
        const downloadUrl = await getPresignedDownloadUrl(downloadKey, 3600)
        
        return NextResponse.json({
          message: "Download URL generated successfully",
          downloadUrl,
          key: downloadKey
        })

      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 })
    }

  } catch (error) {
    console.error("S3 test error:", error)
    return NextResponse.json({ 
      error: "S3 operation failed",
      details: error instanceof Error ? error.message : "Unknown error",
      configured: isS3Configured()
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    if (!isS3Configured()) {
      return NextResponse.json({ 
        error: "S3 not configured" 
      }, { status: 503 })
    }

    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')
    
    if (!key) {
      return NextResponse.json({ error: "Key parameter required" }, { status: 400 })
    }

    await deleteFile(key)
    
    return NextResponse.json({
      message: "File deleted successfully",
      key
    })

  } catch (error) {
    console.error("S3 delete error:", error)
    return NextResponse.json({ 
      error: "Delete operation failed",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    if (!isS3Configured()) {
      return NextResponse.json({ 
        error: "S3 not configured" 
      }, { status: 503 })
    }

    const body = await request.json()
    const { fileName, contentType, content } = body

    if (!fileName || !contentType) {
      return NextResponse.json({ 
        error: "fileName and contentType are required" 
      }, { status: 400 })
    }

    // Generate upload URL
    const key = `test/${Date.now()}-${fileName}`
    const uploadUrl = await getPresignedUploadUrl(key, contentType, 3600)

    // If content is provided, we'll return instructions for upload
    const response = {
      message: "Upload URL generated for test file",
      uploadUrl,
      key,
      publicUrl: `${process.env.NEXT_PUBLIC_S3_PUBLIC_URL}/${key}`,
      instructions: content ? 
        "Use the uploadUrl to PUT the content, then call GET with action=download-url&key=" + key :
        "Use the uploadUrl to upload your file"
    }

    if (content) {
      response.content = content
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error("S3 upload preparation error:", error)
    return NextResponse.json({ 
      error: "Upload preparation failed",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
