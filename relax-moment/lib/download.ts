/**
 * Download utilities for handling file downloads
 */

/**
 * Download a file from a URL
 * @param url - The URL of the file to download
 * @param filename - The desired filename for the download
 */
export async function downloadFile(url: string, filename: string): Promise<void> {
  try {
    // Create a temporary anchor element to trigger download
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.target = '_blank'
    
    // Add to DOM, click, and remove
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('Download failed:', error)
    throw new Error('Failed to download file')
  }
}

/**
 * Generate a filename for an episode audio file
 * @param episodeTitle - The title of the episode
 * @param audioUrl - The URL of the audio file
 * @returns A sanitized filename
 */
export function generateAudioFilename(episodeTitle: string, audioUrl: string): string {
  // Get file extension from URL
  const urlParts = audioUrl.split('.')
  const extension = urlParts.length > 1 ? urlParts[urlParts.length - 1].split('?')[0] : 'mp3'
  
  // Sanitize title for filename
  const sanitizedTitle = episodeTitle
    .replace(/[^a-zA-Z0-9\s-_]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .toLowerCase()
    .substring(0, 50) // Limit length
  
  return `${sanitizedTitle}.${extension}`
}

/**
 * Check if a URL is downloadable (same origin or CORS enabled)
 * @param url - The URL to check
 * @returns Promise that resolves to true if downloadable
 */
export async function isDownloadable(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch (error) {
    // If HEAD request fails, try to download anyway
    return true
  }
}

/**
 * Download episode audio with proper error handling
 * @param episode - Episode object with title and audio_url
 * @returns Promise that resolves when download starts
 */
export async function downloadEpisodeAudio(episode: { title: string; audio_url: string }): Promise<void> {
  const filename = generateAudioFilename(episode.title, episode.audio_url)
  
  // Check if the file is downloadable
  const canDownload = await isDownloadable(episode.audio_url)
  
  if (!canDownload) {
    throw new Error('File is not accessible for download')
  }
  
  await downloadFile(episode.audio_url, filename)
}
