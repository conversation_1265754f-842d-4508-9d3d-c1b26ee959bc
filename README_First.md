


BILLs-MacBook-Air:relax-moment bill$ ./scripts/build-docker.sh

docker tag relax-moment:latest gcr.io/goog-ai-101/relax-moment:latest
docker push gcr.io/goog-ai-101/relax-moment:latest


# Deploy GCP Cloud Run
gcloud run deploy relax-moment \
    --image gcr.io/goog-ai-101/relax-moment:latest \
    --platform managed \
    --region us-west1 \
    --allow-unauthenticated \
    --set-env-vars "S3_ACCESS_KEY=********************" \
    --set-env-vars "S3_SECRET_KEY=QEueQ66he8AEC4m8O5JWHXGS33rm98QSaP2vmdJ9" \
    --set-env-vars "S3_BUCKET_NAME=relaxmoment-bill" \
    --set-env-vars "S3_REGION=us-west-2" \
    --set-env-vars "NEXT_PUBLIC_S3_PUBLIC_URL=https://relaxmoment-bill.s3.amazonaws.com" \
    --set-env-vars "NEXT_PUBLIC_SUPABASE_URL=https://iznqccpmmsgsdbcfwtmb.supabase.co" \
    --set-env-vars "NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ZD3EqI_x9T9xijnHskXnhy5gMR7HdSUh9xzOIKoRCss" \
    --memory 1Gi \
    --port 8080

https://relax-moment-880404139350.us-west1.run.app

https://relax-moment-fwmrpcsejq-uw.a.run.app/api/podcast-rss

# 運行這個命令來獲取你的服務 URL
gcloud run services list --filter="metadata.name:relax-moment" --format="value(status.url)"