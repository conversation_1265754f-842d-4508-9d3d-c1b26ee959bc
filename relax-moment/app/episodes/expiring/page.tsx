"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar, Clock, Edit, MoreHorizontal, Trash } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Sample data for expiring episodes
const expiringEpisodes = [
  {
    id: "3",
    title: "Exploring New Web Development Trends",
    publishDate: "2025-06-15",
    expiryDate: "2025-07-05",
    daysRemaining: 5,
    duration: "28:47",
    status: "expiring-soon",
    category: "Technology",
  },
  {
    id: "5",
    title: "Health and Wellness in the Digital Age",
    publishDate: "2025-06-05",
    expiryDate: "2025-07-05",
    daysRemaining: 5,
    duration: "38:12",
    status: "expiring-soon",
    category: "Health",
  },
  {
    id: "6",
    title: "Financial Planning for Entrepreneurs",
    publishDate: "2025-06-01",
    expiryDate: "2025-07-01",
    daysRemaining: 1,
    duration: "42:55",
    status: "expiring-soon",
    category: "Business",
  },
]

export default function ExpiringEpisodesPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Expiring Episodes</h1>
        <p className="text-muted-foreground">Manage episodes that are about to expire</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-orange-500" />
            Expiring Soon
          </CardTitle>
          <CardDescription>Episodes that will expire in the next 7 days</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead className="hidden md:table-cell">Category</TableHead>
                <TableHead className="hidden md:table-cell">Published</TableHead>
                <TableHead>Expires</TableHead>
                <TableHead>Days Left</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {expiringEpisodes.map((episode) => (
                <TableRow key={episode.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-9 w-9">
                        <AvatarImage src={`/placeholder.svg?height=36&width=36`} alt={episode.title} />
                        <AvatarFallback>EP</AvatarFallback>
                      </Avatar>
                      <div className="font-medium">{episode.title}</div>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">{episode.category}</TableCell>
                  <TableCell className="hidden md:table-cell">{episode.publishDate}</TableCell>
                  <TableCell>{episode.expiryDate}</TableCell>
                  <TableCell>
                    <Badge variant={episode.daysRemaining <= 3 ? "destructive" : "warning"}>
                      {episode.daysRemaining} {episode.daysRemaining === 1 ? "day" : "days"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Calendar className="mr-2 h-4 w-4" />
                          Extend Expiry
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-destructive">
                          <Trash className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-red-500" />
            Recently Expired
          </CardTitle>
          <CardDescription>Episodes that have expired in the last 30 days</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead className="hidden md:table-cell">Category</TableHead>
                <TableHead className="hidden md:table-cell">Published</TableHead>
                <TableHead>Expired On</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={`/placeholder.svg?height=36&width=36`} alt="The Psychology of Productivity" />
                      <AvatarFallback>EP</AvatarFallback>
                    </Avatar>
                    <div className="font-medium">The Psychology of Productivity</div>
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">Education</TableCell>
                <TableCell className="hidden md:table-cell">2025-05-25</TableCell>
                <TableCell>2025-06-25</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem>
                        <Calendar className="mr-2 h-4 w-4" />
                        Restore & Extend
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-destructive">
                        <Trash className="mr-2 h-4 w-4" />
                        Delete Permanently
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={`/placeholder.svg?height=36&width=36`} alt="Sustainable Business Practices" />
                      <AvatarFallback>EP</AvatarFallback>
                    </Avatar>
                    <div className="font-medium">Sustainable Business Practices</div>
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">Business</TableCell>
                <TableCell className="hidden md:table-cell">2025-05-20</TableCell>
                <TableCell>2025-06-20</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem>
                        <Calendar className="mr-2 h-4 w-4" />
                        Restore & Extend
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-destructive">
                        <Trash className="mr-2 h-4 w-4" />
                        Delete Permanently
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
