import { supabase } from "../lib/supabase"

/**
 * This script checks for episodes that are about to expire
 * and updates their status accordingly.
 *
 * It can be run as a scheduled task (e.g., daily cron job)
 */
async function checkExpiringEpisodes() {
  console.log("Checking for expiring episodes...")

  const today = new Date()
  const sevenDaysLater = new Date()
  sevenDaysLater.setDate(today.getDate() + 7)

  try {
    // Find episodes that will expire in the next 7 days
    const { data: expiringEpisodes, error: expiringError } = await supabase
      .from("episodes")
      .select("*")
      .lt("expiry_date", sevenDaysLater.toISOString())
      .gt("expiry_date", today.toISOString())
      .eq("status", "published")

    if (expiringError) {
      throw expiringError
    }

    console.log(`Found ${expiringEpisodes.length} episodes expiring soon`)

    // Update their status to 'expiring-soon'
    if (expiringEpisodes.length > 0) {
      const { error: updateError } = await supabase
        .from("episodes")
        .update({ status: "expiring-soon" })
        .in(
          "id",
          expiringEpisodes.map((ep) => ep.id),
        )

      if (updateError) {
        throw updateError
      }

      console.log("Updated expiring episodes status")
    }

    // Find episodes that have already expired
    const { data: expiredEpisodes, error: expiredError } = await supabase
      .from("episodes")
      .select("*")
      .lt("expiry_date", today.toISOString())
      .not("status", "eq", "expired")

    if (expiredError) {
      throw expiredError
    }

    console.log(`Found ${expiredEpisodes.length} expired episodes`)

    // Update their status to 'expired'
    if (expiredEpisodes.length > 0) {
      const { error: updateError } = await supabase
        .from("episodes")
        .update({ status: "expired" })
        .in(
          "id",
          expiredEpisodes.map((ep) => ep.id),
        )

      if (updateError) {
        throw updateError
      }

      console.log("Updated expired episodes status")
    }

    console.log("Expiration check completed successfully")
    return {
      expiringCount: expiringEpisodes.length,
      expiredCount: expiredEpisodes.length,
    }
  } catch (error) {
    console.error("Error checking expiring episodes:", error)
    throw error
  }
}

// Execute the function
checkExpiringEpisodes()
  .then((result) => {
    console.log("Script completed:", result)
    process.exit(0)
  })
  .catch((error) => {
    console.error("Script failed:", error)
    process.exit(1)
  })
