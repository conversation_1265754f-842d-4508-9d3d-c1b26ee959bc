import { type NextRequest, NextResponse } from "next/server"
import { getPresignedUploadUrl, isS3Configured } from "@/lib/s3"
import { nanoid } from "nanoid"

export async function POST(request: NextRequest) {
  try {
    // Check if S3 is configured
    if (!isS3Configured()) {
      return NextResponse.json({ error: "File storage not configured" }, { status: 503 })
    }
    const body = await request.json()
    const { fileName, fileType, fileSize } = body

    if (!fileName || !fileType) {
      return NextResponse.json({ error: "fileName and fileType are required" }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ["audio/mpeg", "audio/mp3", "audio/wav", "image/jpeg", "image/png"]
    if (!allowedTypes.includes(fileType)) {
      return NextResponse.json({ error: "Unsupported file type" }, { status: 400 })
    }

    // Validate file size (max 500MB for audio, 5MB for images)
    const maxSize = fileType.startsWith("audio") ? 500 * 1024 * 1024 : 5 * 1024 * 1024
    if (fileSize && fileSize > maxSize) {
      return NextResponse.json({ error: "File size exceeds the limit" }, { status: 400 })
    }

    // Generate a unique file key
    const fileExtension = fileName.split(".").pop()
    const uniqueId = nanoid(10)
    const folder = fileType.startsWith("audio") ? "episodes" : "images"
    const key = `${folder}/${uniqueId}-${fileName}`

    // Generate presigned URL
    const presignedUrl = await getPresignedUploadUrl(key, fileType)

    return NextResponse.json({
      uploadUrl: presignedUrl,
      fileKey: key,
      fileUrl: `${process.env.NEXT_PUBLIC_S3_PUBLIC_URL || ""}/${key}`,
    })
  } catch (error) {
    console.error("Error generating upload URL:", error)
    return NextResponse.json({ error: "Failed to generate upload URL" }, { status: 500 })
  }
}
