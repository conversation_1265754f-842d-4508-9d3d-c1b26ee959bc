"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { CalendarIcon, Upload, AlertCircle, CheckCircle } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AudioUploader } from "@/components/upload/AudioUploader"
import { ImageUploader } from "@/components/upload/ImageUploader"
import { validateEpisodeForm, formatTags, tagsToString, type EpisodeFormData, type ValidationError } from "@/lib/validation"

export default function UploadPage() {
  const router = useRouter()
  const { toast } = useToast()

  // Form state
  const [formData, setFormData] = useState<EpisodeFormData>({
    title: '',
    description: '',
    category: '',
    tags: [],
    publishDate: new Date(),
    status: 'draft'
  })

  // Upload state
  const [audioUrl, setAudioUrl] = useState<string>('')
  const [coverImageUrl, setCoverImageUrl] = useState<string>('')
  const [audioMetadata, setAudioMetadata] = useState<{ duration?: number; title?: string }>({})

  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<ValidationError[]>([])
  const [uploadErrors, setUploadErrors] = useState<string[]>([])

  const handleInputChange = (field: keyof EpisodeFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear field-specific errors when user starts typing
    setErrors(prev => prev.filter(error => error.field !== field))
  }

  const handleTagsChange = (tagsString: string) => {
    const tags = formatTags(tagsString)
    handleInputChange('tags', tags)
  }

  const handleAudioUpload = (fileUrl: string, metadata: { duration?: number; title?: string }) => {
    setAudioUrl(fileUrl)
    setAudioMetadata(metadata)

    // Auto-fill title if empty and we have metadata
    if (!formData.title && metadata.title) {
      handleInputChange('title', metadata.title)
    }

    // Clear upload errors
    setUploadErrors(prev => prev.filter(error => !error.includes('audio')))

    toast({
      title: "Audio uploaded successfully!",
      description: "Your audio file has been uploaded to the cloud.",
    })
  }

  const handleImageUpload = (fileUrl: string) => {
    setCoverImageUrl(fileUrl)

    // Clear upload errors
    setUploadErrors(prev => prev.filter(error => !error.includes('image')))

    toast({
      title: "Image uploaded successfully!",
      description: "Your cover image has been uploaded.",
    })
  }

  const handleUploadError = (error: string, type: 'audio' | 'image') => {
    const errorMessage = `${type === 'audio' ? 'Audio' : 'Image'} upload failed: ${error}`
    setUploadErrors(prev => [...prev.filter(e => !e.includes(type)), errorMessage])

    toast({
      title: "Upload failed",
      description: errorMessage,
      variant: "destructive",
    })
  }

  const handleSubmit = async () => {
    // Validate form
    const dataToValidate = {
      ...formData,
      audioUrl,
      coverImageUrl
    }

    const validation = validateEpisodeForm(dataToValidate)

    if (!validation.valid) {
      setErrors(validation.errors)
      toast({
        title: "Validation failed",
        description: "Please fix the errors below and try again.",
        variant: "destructive",
      })
      return
    }

    // Check if audio is uploaded
    if (!audioUrl) {
      toast({
        title: "Audio file required",
        description: "Please upload an audio file before submitting.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const episodeData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        tags: formData.tags,
        publish_date: formData.publishDate.toISOString(),
        expiry_date: formData.expiryDate?.toISOString(),
        status: formData.status,
        audio_url: audioUrl,
        cover_image_url: coverImageUrl || null,
        duration: audioMetadata.duration ? Math.floor(audioMetadata.duration / 60) + ':' + String(Math.floor(audioMetadata.duration % 60)).padStart(2, '0') : null
      }

      const response = await fetch('/api/episodes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(episodeData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create episode')
      }

      const result = await response.json()

      toast({
        title: "Episode created successfully!",
        description: "Your podcast episode has been saved and is ready for publishing.",
      })

      // Redirect to episodes page
      router.push('/episodes')

    } catch (error) {
      console.error('Submit error:', error)
      toast({
        title: "Failed to create episode",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getFieldError = (field: string): string | undefined => {
    const error = errors.find(e => e.field === field)
    return error?.message
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Upload Episode</h1>
        <p className="text-muted-foreground">Upload a new podcast episode and set its metadata</p>
      </div>

      {/* Upload Errors */}
      {uploadErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1">
              {uploadErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 lg:grid-cols-2">
        {/* File Uploads */}
        <div className="space-y-6">
          <AudioUploader
            onUploadComplete={handleAudioUpload}
            onUploadError={(error) => handleUploadError(error, 'audio')}
            disabled={isSubmitting}
          />

          <ImageUploader
            onUploadComplete={handleImageUpload}
            onUploadError={(error) => handleUploadError(error, 'image')}
            disabled={isSubmitting}
          />
        </div>

        {/* Episode Information */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Episode Information</CardTitle>
              <CardDescription>
                Provide details about your podcast episode
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter episode title"
                  disabled={isSubmitting}
                />
                {getFieldError('title') && (
                  <p className="text-sm text-destructive">{getFieldError('title')}</p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe your episode content..."
                  rows={4}
                  disabled={isSubmitting}
                />
                {getFieldError('description') && (
                  <p className="text-sm text-destructive">{getFieldError('description')}</p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => handleInputChange('category', value)}
                  disabled={isSubmitting}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="technology">Technology</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="education">Education</SelectItem>
                    <SelectItem value="entertainment">Entertainment</SelectItem>
                    <SelectItem value="health">Health & Fitness</SelectItem>
                    <SelectItem value="news">News & Politics</SelectItem>
                  </SelectContent>
                </Select>
                {getFieldError('category') && (
                  <p className="text-sm text-destructive">{getFieldError('category')}</p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  value={tagsToString(formData.tags)}
                  onChange={(e) => handleTagsChange(e.target.value)}
                  placeholder="Enter tags separated by commas"
                  disabled={isSubmitting}
                />
                <p className="text-xs text-muted-foreground">
                  Separate tags with commas (max 10 tags)
                </p>
                {getFieldError('tags') && (
                  <p className="text-sm text-destructive">{getFieldError('tags')}</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Publishing Settings</CardTitle>
              <CardDescription>
                Configure when and how your episode will be published
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="publish-date">Publish Date *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        id="publish-date"
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.publishDate && "text-muted-foreground"
                        )}
                        disabled={isSubmitting}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.publishDate ? format(formData.publishDate, "PPP") : "Select date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.publishDate}
                        onSelect={(date) => date && handleInputChange('publishDate', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  {getFieldError('publishDate') && (
                    <p className="text-sm text-destructive">{getFieldError('publishDate')}</p>
                  )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="expiry-date">Expiry Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        id="expiry-date"
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.expiryDate && "text-muted-foreground"
                        )}
                        disabled={isSubmitting}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.expiryDate ? format(formData.expiryDate, "PPP") : "Select date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.expiryDate}
                        onSelect={(date) => handleInputChange('expiryDate', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  {getFieldError('expiryDate') && (
                    <p className="text-sm text-destructive">{getFieldError('expiryDate')}</p>
                  )}
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: 'draft' | 'published') => handleInputChange('status', value)}
                  disabled={isSubmitting}
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                  </SelectContent>
                </Select>
                {getFieldError('status') && (
                  <p className="text-sm text-destructive">{getFieldError('status')}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button
          variant="outline"
          onClick={() => router.push('/episodes')}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || !audioUrl}
          className="min-w-[120px]"
        >
          {isSubmitting ? (
            <>
              <Upload className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            <>
              <CheckCircle className="mr-2 h-4 w-4" />
              Create Episode
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
