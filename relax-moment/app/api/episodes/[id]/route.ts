import { NextRequest, NextResponse } from "next/server"
import { supabase, isSupabaseConfigured } from "@/lib/supabase"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Database not configured" }, { status: 503 })
    }

    const { data: episode, error } = await supabase
      .from('episodes')
      .select('*')
      .eq('id', params.id)
      .single()

    if (error || !episode) {
      return NextResponse.json({ error: "Episode not found" }, { status: 404 })
    }

    return NextResponse.json({ episode })

  } catch (error) {
    console.error("Get episode error:", error)
    return NextResponse.json({ 
      error: "Unexpected error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Database not configured" }, { status: 503 })
    }

    const body = await request.json()
    const { status, title, description, category, tags, publish_date, expiry_date } = body

    // Build update object with only provided fields
    const updateData: any = {}
    if (status !== undefined) updateData.status = status
    if (title !== undefined) updateData.title = title
    if (description !== undefined) updateData.description = description
    if (category !== undefined) updateData.category = category
    if (tags !== undefined) updateData.tags = tags
    if (publish_date !== undefined) updateData.publish_date = publish_date
    if (expiry_date !== undefined) updateData.expiry_date = expiry_date

    // Add updated timestamp
    updateData.updated_at = new Date().toISOString()

    const { data: episode, error } = await supabase
      .from('episodes')
      .update(updateData)
      .eq('id', params.id)
      .select()
      .single()

    if (error) {
      console.error("Update episode error:", error)
      return NextResponse.json({ 
        error: "Failed to update episode", 
        details: error.message 
      }, { status: 500 })
    }

    if (!episode) {
      return NextResponse.json({ error: "Episode not found" }, { status: 404 })
    }

    return NextResponse.json({ episode })

  } catch (error) {
    console.error("Update episode error:", error)
    return NextResponse.json({ 
      error: "Unexpected error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Database not configured" }, { status: 503 })
    }

    const { error } = await supabase
      .from('episodes')
      .delete()
      .eq('id', params.id)

    if (error) {
      console.error("Delete episode error:", error)
      return NextResponse.json({ 
        error: "Failed to delete episode", 
        details: error.message 
      }, { status: 500 })
    }

    return NextResponse.json({ message: "Episode deleted successfully" })

  } catch (error) {
    console.error("Delete episode error:", error)
    return NextResponse.json({ 
      error: "Unexpected error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
