import type { Episode, RssFeed } from "./types"

// Generate RSS XML for Apple Podcasts
export function generateRssFeed(feed: RssFeed, episodes: Episode[]): string {
  const now = new Date().toUTCString()

  // Filter out expired episodes
  const activeEpisodes = episodes.filter((episode) => episode.status !== "expired")

  // Build the XML
  let xml = `<?xml version="1.0" encoding="UTF-8"?>
<rss xmlns:itunes="http://www.itunes.com/dtds/podcast-1.0.dtd" 
     xmlns:content="http://purl.org/rss/1.0/modules/content/"
     xmlns:atom="http://www.w3.org/2005/Atom"
     version="2.0">
  <channel>
    <title>${escapeXml(feed.title)}</title>
    <link>${escapeXml(feed.url)}</link>
    <language>${feed.language}</language>
    <pubDate>${now}</pubDate>
    <lastBuildDate>${now}</lastBuildDate>
    <atom:link href="${escapeXml(feed.url)}" rel="self" type="application/rss+xml" />
    <copyright>${escapeXml(feed.copyright)}</copyright>
    <itunes:author>${escapeXml(feed.author)}</itunes:author>
    <itunes:owner>
      <itunes:name>${escapeXml(feed.author)}</itunes:name>
      <itunes:email>${escapeXml(feed.email)}</itunes:email>
    </itunes:owner>
    <itunes:image href="${escapeXml(feed.imageUrl)}"/>
    <itunes:category text="${escapeXml(feed.category)}">
      <itunes:category text="${escapeXml(feed.subcategory)}"/>
    </itunes:category>
    <itunes:explicit>${feed.explicit ? "yes" : "no"}</itunes:explicit>
    <description>
      ${escapeXml(feed.description)}
    </description>\n`

  // Add episodes
  activeEpisodes.forEach((episode) => {
    const pubDate = new Date(episode.publishDate).toUTCString()

    xml += `    <item>
      <title>${escapeXml(episode.title)}</title>
      <itunes:author>${escapeXml(feed.author)}</itunes:author>
      <description>
        ${escapeXml(episode.description)}
      </description>
      <enclosure url="${escapeXml(episode.audioUrl)}" 
                length="0" type="audio/mpeg"/>
      <guid isPermaLink="false">${escapeXml(episode.id)}</guid>
      <pubDate>${pubDate}</pubDate>
      <itunes:duration>${episode.duration}</itunes:duration>
      <itunes:image href="${escapeXml(episode.coverImageUrl)}"/>
    </item>\n`
  })

  xml += `  </channel>
</rss>`

  return xml
}

// Helper function to escape XML special characters
function escapeXml(unsafe: string): string {
  return unsafe.replace(/[<>&'"]/g, (c) => {
    switch (c) {
      case "<":
        return "&lt;"
      case ">":
        return "&gt;"
      case "&":
        return "&amp;"
      case "'":
        return "&apos;"
      case '"':
        return "&quot;"
      default:
        return c
    }
  })
}

// Validate RSS feed against Apple Podcasts requirements
export function validateRssFeed(feed: RssFeed, episodes: Episode[]): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // Check required feed fields
  if (!feed.title) errors.push("Feed title is required")
  if (!feed.description) errors.push("Feed description is required")
  if (!feed.author) errors.push("Feed author is required")
  if (!feed.email) errors.push("Feed email is required")
  if (!feed.imageUrl) errors.push("Feed image URL is required")
  if (!feed.category) errors.push("iTunes category is required")

  // Check image URL format
  if (feed.imageUrl && !feed.imageUrl.startsWith("https://")) {
    errors.push("Feed image URL must use HTTPS")
  }

  // Check episodes
  if (episodes.length === 0) {
    errors.push("Feed must contain at least one episode")
  }

  // Check each episode
  episodes.forEach((episode, index) => {
    if (!episode.title) errors.push(`Episode ${index + 1}: Title is required`)
    if (!episode.audioUrl) errors.push(`Episode ${index + 1}: Audio URL is required`)
    if (!episode.description) errors.push(`Episode ${index + 1}: Description is required`)

    // Check audio URL format
    if (episode.audioUrl && !episode.audioUrl.startsWith("https://")) {
      errors.push(`Episode ${index + 1}: Audio URL must use HTTPS`)
    }
  })

  return {
    valid: errors.length === 0,
    errors,
  }
}
