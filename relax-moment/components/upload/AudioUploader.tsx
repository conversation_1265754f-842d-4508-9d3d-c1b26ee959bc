"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Upload, Music, X, Play, Pause } from "lucide-react"
import { uploadFile, formatFileSize, formatDuration, getAudioMetadata, type UploadProgress } from "@/lib/upload"

interface AudioUploaderProps {
  onUploadComplete: (fileUrl: string, metadata: { duration?: number; title?: string }) => void
  onUploadError: (error: string) => void
  disabled?: boolean
}

export function AudioUploader({ onUploadComplete, onUploadError, disabled }: AudioUploaderProps) {
  const [file, setFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState<UploadProgress | null>(null)
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [metadata, setMetadata] = useState<{ duration?: number; title?: string } | null>(null)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)

  const handleFileSelect = async (selectedFile: File) => {
    setFile(selectedFile)
    setProgress(null)
    
    // Create preview URL
    const url = URL.createObjectURL(selectedFile)
    setAudioUrl(url)
    
    // Extract metadata
    try {
      const meta = await getAudioMetadata(selectedFile)
      setMetadata(meta)
    } catch (error) {
      console.error('Failed to extract metadata:', error)
      setMetadata({ title: selectedFile.name.replace(/\.[^/.]+$/, '') })
    }
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      handleFileSelect(selectedFile)
    }
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      handleFileSelect(droppedFile)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
  }

  const handleUpload = async () => {
    if (!file) return

    setUploading(true)
    setProgress({ loaded: 0, total: file.size, percentage: 0 })

    try {
      const result = await uploadFile(file, (prog) => {
        setProgress(prog)
      })

      if (result.success && result.fileUrl) {
        onUploadComplete(result.fileUrl, metadata || {})
      } else {
        onUploadError(result.error || 'Upload failed')
      }
    } catch (error) {
      onUploadError(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setUploading(false)
    }
  }

  const handleRemove = () => {
    setFile(null)
    setAudioUrl(null)
    setMetadata(null)
    setProgress(null)
    setIsPlaying(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl)
    }
  }

  const togglePlayback = () => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play()
    }
    setIsPlaying(!isPlaying)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="h-5 w-5" />
          Audio File
        </CardTitle>
        <CardDescription>
          Upload your podcast episode audio file (MP3 or WAV, max 500MB)
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!file ? (
          <div
            className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors cursor-pointer"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg font-medium mb-2">Drop your audio file here</p>
            <p className="text-sm text-muted-foreground mb-4">
              or click to browse files
            </p>
            <Button variant="outline" disabled={disabled}>
              Browse Files
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="audio/mp3,audio/mpeg,audio/wav"
              onChange={handleFileChange}
              className="hidden"
              disabled={disabled}
            />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Music className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="font-medium">{file.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatFileSize(file.size)}
                    {metadata?.duration && ` • ${formatDuration(metadata.duration)}`}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {audioUrl && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={togglePlayback}
                    disabled={uploading}
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleRemove}
                  disabled={uploading}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {audioUrl && (
              <audio
                ref={audioRef}
                src={audioUrl}
                onEnded={() => setIsPlaying(false)}
                onPause={() => setIsPlaying(false)}
                onPlay={() => setIsPlaying(true)}
                className="hidden"
              />
            )}

            {uploading && progress && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Uploading...</span>
                  <span>{progress.percentage}%</span>
                </div>
                <Progress value={progress.percentage} />
              </div>
            )}

            {!uploading && (
              <Button onClick={handleUpload} disabled={disabled} className="w-full">
                <Upload className="h-4 w-4 mr-2" />
                Upload Audio File
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
