import { NextResponse } from "next/server"
import { supabase, isSupabaseConfigured } from "@/lib/supabase"

export async function GET() {
  try {
    // Check if Supabase is configured
    if (!isSupabaseConfigured()) {
      return new NextResponse("Database not configured", { status: 503 })
    }

    // Get published episodes
    const { data: episodes, error: episodesError } = await supabase
      .from('episodes')
      .select('*')
      .eq('status', 'published')
      .order('publish_date', { ascending: false })

    if (episodesError) {
      console.error("Error fetching episodes:", episodesError)
      return new NextResponse("Failed to fetch episodes", { status: 500 })
    }

    // Get RSS feed settings (use the first active feed or create default)
    const { data: feedData } = await supabase
      .from('rss_feeds')
      .select('*')
      .eq('is_active', true)
      .single()

    // Clean up any invalid URLs from database data
    if (feedData && feedData.image_url && feedData.image_url.includes('example.com')) {
      feedData.image_url = null
    }

    // Use feed data or defaults
    const baseUrl = process.env.NEXT_PUBLIC_S3_PUBLIC_URL || "https://relaxmoment-bill.s3.amazonaws.com"
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://relax-moment-fwmrpcsejq-uw.a.run.app"

    const feed = feedData || {
      title: "Tech Talk Podcast",
      description: "A podcast about technology, development, and innovation",
      author: "Tech Talk Team",
      email: "<EMAIL>",
      image_url: `${baseUrl}/images/default-podcast-cover.jpg`,
      language: "en-us",
      category: "Technology",
      subcategory: "Software How-To",
      copyright: "© 2025 Tech Talk Podcast",
      url: `${siteUrl}/api/podcast-rss`,
      explicit: false
    }

    // Ensure feed image_url is valid
    if (!feed.image_url || feed.image_url.includes('example.com')) {
      feed.image_url = `${baseUrl}/images/default-podcast-cover.jpg`
    }

    // Generate RSS XML
    const rssXml = generatePodcastRSS(feed, episodes)

    // Return RSS feed as XML
    return new NextResponse(rssXml, {
      headers: {
        "Content-Type": "application/xml; charset=utf-8",
        "Cache-Control": "public, max-age=300, s-maxage=600",
      },
    })
  } catch (error) {
    console.error("Error generating RSS feed:", error)
    return new NextResponse("Internal server error", { status: 500 })
  }
}

function generatePodcastRSS(feed: any, episodes: any[]): string {
  const now = new Date().toUTCString()
  const baseUrl = process.env.NEXT_PUBLIC_S3_PUBLIC_URL || "https://relaxmoment-bill.s3.amazonaws.com"
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://relax-moment-fwmrpcsejq-uw.a.run.app"

  let xml = `<?xml version="1.0" encoding="UTF-8"?>
<rss xmlns:itunes="http://www.itunes.com/dtds/podcast-1.0.dtd" 
     xmlns:content="http://purl.org/rss/1.0/modules/content/"
     xmlns:atom="http://www.w3.org/2005/Atom"
     version="2.0">
  <channel>
    <title>${escapeXml(feed.title)}</title>
    <link>${siteUrl}</link>
    <language>${feed.language || 'en-us'}</language>
    <pubDate>${now}</pubDate>
    <lastBuildDate>${now}</lastBuildDate>
    <atom:link href="${siteUrl}/api/podcast-rss" rel="self" type="application/rss+xml" />
    <copyright>${escapeXml(feed.copyright || '© 2025 Podcast')}</copyright>
    <itunes:author>${escapeXml(feed.author)}</itunes:author>
    <itunes:owner>
      <itunes:name>${escapeXml(feed.author)}</itunes:name>
      <itunes:email>${escapeXml(feed.email)}</itunes:email>
    </itunes:owner>
    <itunes:image href="${escapeXml(getValidImageUrl(feed.image_url, baseUrl))}"/>
    <image>
      <url>${escapeXml(getValidImageUrl(feed.image_url, baseUrl))}</url>
      <title>${escapeXml(feed.title)}</title>
      <link>${siteUrl}</link>
    </image>
    <itunes:category text="${escapeXml(feed.category || 'Technology')}">
      <itunes:category text="${escapeXml(feed.subcategory || 'Software How-To')}"/>
    </itunes:category>
    <itunes:explicit>${feed.explicit ? 'yes' : 'no'}</itunes:explicit>
    <description><![CDATA[${feed.description}]]></description>
`

  // Add episodes
  episodes.forEach((episode) => {
    const pubDate = new Date(episode.publish_date).toUTCString()
    const audioUrl = episode.audio_url
    const coverImage = getValidImageUrl(episode.cover_image_url, baseUrl, feed.image_url)

    // Extract file size from audio URL if possible (for now, use 0)
    const fileSize = "0"

    // Determine audio type
    const audioType = audioUrl.toLowerCase().includes('.wav') ? 'audio/wav' : 'audio/mpeg'

    xml += `    <item>
      <title>${escapeXml(episode.title)}</title>
      <itunes:author>${escapeXml(feed.author)}</itunes:author>
      <description><![CDATA[${episode.description}]]></description>
      <enclosure url="${escapeXml(audioUrl)}"
                length="${fileSize}" type="${audioType}"/>
      <guid isPermaLink="false">${escapeXml(episode.id)}</guid>
      <pubDate>${pubDate}</pubDate>
      <itunes:duration>${episode.duration || '00:00'}</itunes:duration>
      <itunes:image href="${escapeXml(coverImage)}"/>
      <link>${siteUrl}/episodes/${episode.id}</link>
    </item>
`
  })

  xml += `  </channel>
</rss>`

  return xml
}

// Helper function to get valid image URL
function getValidImageUrl(imageUrl: string | null | undefined, baseUrl: string, fallbackUrl?: string): string {
  // Check if we have a valid image URL (not example.com and not empty)
  if (imageUrl && imageUrl.trim() && !imageUrl.includes('example.com') && imageUrl.startsWith('http')) {
    return imageUrl
  }

  // Check if we have a valid fallback URL
  if (fallbackUrl && fallbackUrl.trim() && !fallbackUrl.includes('example.com') && fallbackUrl.startsWith('http')) {
    return fallbackUrl
  }

  // Use a default podcast cover image - create a proper default
  return `${baseUrl}/images/default-podcast-cover.jpg`
}

// Helper function to escape XML special characters
function escapeXml(unsafe: string | null | undefined): string {
  if (!unsafe) return ''

  return String(unsafe).replace(/[<>&'"]/g, (c) => {
    switch (c) {
      case "<": return "&lt;"
      case ">": return "&gt;"
      case "&": return "&amp;"
      case "'": return "&apos;"
      case '"': return "&quot;"
      default: return c
    }
  })
}
