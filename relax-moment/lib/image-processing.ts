import sharp from 'sharp'

// Apple Podcasts image requirements
export const APPLE_PODCASTS_IMAGE_SIZE = 1400
export const APPLE_PODCASTS_MAX_SIZE = 3000
export const APPLE_PODCASTS_MIN_SIZE = 1400

// Supported image formats
export const SUPPORTED_IMAGE_FORMATS = ['jpeg', 'jpg', 'png', 'webp'] as const
export type SupportedImageFormat = typeof SUPPORTED_IMAGE_FORMATS[number]

export interface ImageProcessingOptions {
  targetSize?: number
  quality?: number
  format?: 'jpeg' | 'png'
  maintainAspectRatio?: boolean
}

export interface ImageProcessingResult {
  buffer: Buffer
  format: string
  width: number
  height: number
  size: number
}

/**
 * Check if an image format is supported
 */
export function isSupportedImageFormat(format: string): format is SupportedImageFormat {
  return SUPPORTED_IMAGE_FORMATS.includes(format.toLowerCase() as SupportedImageFormat)
}

/**
 * Get image metadata without processing
 */
export async function getImageMetadata(buffer: Buffer) {
  try {
    const metadata = await sharp(buffer).metadata()
    return {
      width: metadata.width || 0,
      height: metadata.height || 0,
      format: metadata.format || 'unknown',
      size: buffer.length
    }
  } catch (error) {
    throw new Error(`Failed to read image metadata: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Check if image meets Apple Podcasts requirements
 */
export function meetsApplePodcastsRequirements(width: number, height: number): boolean {
  // Must be square
  if (width !== height) return false
  
  // Must be within size range
  return width >= APPLE_PODCASTS_MIN_SIZE && width <= APPLE_PODCASTS_MAX_SIZE
}

/**
 * Determine if image needs processing
 */
export function needsProcessing(width: number, height: number): boolean {
  return !meetsApplePodcastsRequirements(width, height)
}

/**
 * Process image to meet Apple Podcasts requirements
 */
export async function processImageForApplePodcasts(
  buffer: Buffer,
  options: ImageProcessingOptions = {}
): Promise<ImageProcessingResult> {
  const {
    targetSize = APPLE_PODCASTS_IMAGE_SIZE,
    quality = 90,
    format = 'jpeg',
    maintainAspectRatio = false
  } = options

  try {
    // Get original metadata
    const originalMetadata = await getImageMetadata(buffer)
    
    // Validate target size
    if (targetSize < APPLE_PODCASTS_MIN_SIZE || targetSize > APPLE_PODCASTS_MAX_SIZE) {
      throw new Error(`Target size ${targetSize} is outside Apple Podcasts requirements (${APPLE_PODCASTS_MIN_SIZE}-${APPLE_PODCASTS_MAX_SIZE})`)
    }

    let sharpInstance = sharp(buffer)

    // Handle non-square images
    if (originalMetadata.width !== originalMetadata.height) {
      if (maintainAspectRatio) {
        // Resize to fit within target size, then pad to make square
        const size = Math.min(originalMetadata.width, originalMetadata.height)
        sharpInstance = sharpInstance
          .resize(targetSize, targetSize, {
            fit: 'contain',
            background: { r: 255, g: 255, b: 255, alpha: 1 }
          })
      } else {
        // Crop to square using the center
        const size = Math.min(originalMetadata.width, originalMetadata.height)
        const left = Math.floor((originalMetadata.width - size) / 2)
        const top = Math.floor((originalMetadata.height - size) / 2)
        
        sharpInstance = sharpInstance
          .extract({ left, top, width: size, height: size })
          .resize(targetSize, targetSize)
      }
    } else {
      // Already square, just resize
      sharpInstance = sharpInstance.resize(targetSize, targetSize)
    }

    // Apply format and quality settings
    if (format === 'jpeg') {
      sharpInstance = sharpInstance.jpeg({ quality, mozjpeg: true })
    } else if (format === 'png') {
      sharpInstance = sharpInstance.png({ quality, compressionLevel: 6 })
    }

    // Process the image
    const processedBuffer = await sharpInstance.toBuffer()
    const processedMetadata = await getImageMetadata(processedBuffer)

    return {
      buffer: processedBuffer,
      format: format,
      width: processedMetadata.width,
      height: processedMetadata.height,
      size: processedBuffer.length
    }

  } catch (error) {
    throw new Error(`Image processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Convert File to Buffer (for browser usage)
 */
export function fileToBuffer(file: File): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      if (reader.result instanceof ArrayBuffer) {
        resolve(Buffer.from(reader.result))
      } else {
        reject(new Error('Failed to read file as ArrayBuffer'))
      }
    }
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsArrayBuffer(file)
  })
}

/**
 * Get recommended filename for processed image
 */
export function getProcessedFilename(originalFilename: string, format: 'jpeg' | 'png' = 'jpeg'): string {
  const nameWithoutExt = originalFilename.replace(/\.[^/.]+$/, '')
  const extension = format === 'jpeg' ? 'jpg' : 'png'
  return `${nameWithoutExt}.${extension}`
}

/**
 * Validate image file before processing
 */
export async function validateImageFile(buffer: Buffer): Promise<{
  valid: boolean
  error?: string
  metadata?: ReturnType<typeof getImageMetadata> extends Promise<infer T> ? T : never
}> {
  try {
    const metadata = await getImageMetadata(buffer)
    
    // Check if format is supported
    if (!isSupportedImageFormat(metadata.format)) {
      return {
        valid: false,
        error: `Unsupported image format: ${metadata.format}. Supported formats: ${SUPPORTED_IMAGE_FORMATS.join(', ')}`
      }
    }

    // Check minimum dimensions
    if (metadata.width < 100 || metadata.height < 100) {
      return {
        valid: false,
        error: 'Image is too small. Minimum size is 100x100 pixels.'
      }
    }

    // Check maximum dimensions (reasonable limit)
    if (metadata.width > 10000 || metadata.height > 10000) {
      return {
        valid: false,
        error: 'Image is too large. Maximum size is 10000x10000 pixels.'
      }
    }

    return {
      valid: true,
      metadata
    }

  } catch (error) {
    return {
      valid: false,
      error: `Failed to validate image: ${error instanceof Error ? error.message : 'Unknown error'}`
    }
  }
}
