# Phase 2 計劃 - Upload 功能實作

## 目標
實作完整的 Episode 上傳功能，包含文件上傳到 S3 和 metadata 存儲到 Supabase。

## 前置作業

### ✅ S3 配置驗證
- 測試 S3 連接和 CRUD 操作
- 驗證文件上傳、下載、刪除功能
- 確認預簽名 URL 生成正常

### ✅ 環境變數確認
```env
S3_REGION=us-west-2
S3_ACCESS_KEY=[已配置]
S3_SECRET_KEY=[已配置]
S3_BUCKET_NAME=relaxmoment-bill
NEXT_PUBLIC_S3_PUBLIC_URL=https://relaxmoment-bill.s3.amazonaws.com
```

## Upload 頁面功能規劃

### 🎯 核心功能
1. **音頻文件上傳**
   - 支援格式：MP3, WAV
   - 文件大小限制：500MB
   - 進度顯示
   - 預覽播放

2. **封面圖片上傳**
   - 支援格式：JPG, PNG
   - 文件大小限制：5MB
   - 圖片預覽
   - 建議尺寸：1400x1400px

3. **Episode Metadata**
   - 標題 (必填)
   - 描述 (必填)
   - 分類選擇
   - 標籤輸入
   - 發布日期
   - 過期日期
   - 狀態選擇 (Draft/Published)

### 🔄 工作流程
```
1. 用戶選擇音頻文件 → 2. 上傳到 S3 → 3. 獲取 S3 URL
                    ↓
4. 用戶填寫 metadata → 5. 提交表單 → 6. 存儲到 Supabase
                    ↓
7. 顯示成功消息 → 8. 重定向到 Episodes 頁面
```

## 技術實作計劃

### 📁 文件結構
```
app/upload/page.tsx          # 主要上傳頁面
components/upload/
  ├── AudioUploader.tsx      # 音頻上傳組件
  ├── ImageUploader.tsx      # 圖片上傳組件
  ├── EpisodeForm.tsx        # Episode 表單組件
  └── UploadProgress.tsx     # 上傳進度組件
lib/
  ├── upload.ts              # 上傳工具函數
  └── validation.ts          # 表單驗證
```

### 🔧 API 端點
1. **`/api/upload/presigned`** - 生成預簽名 URL
2. **`/api/episodes`** - 創建 Episode (已存在，需增強)
3. **`/api/upload/validate`** - 文件驗證

### 📋 表單驗證規則
```typescript
interface EpisodeFormData {
  title: string           // 必填，最少 3 字符
  description: string     // 必填，最少 10 字符
  category: string        // 必填，預定義選項
  tags: string[]         // 可選，最多 10 個
  publishDate: Date      // 必填，不能是過去時間
  expiryDate?: Date      // 可選，必須晚於發布日期
  status: 'draft' | 'published'  // 必填
  audioFile: File        // 必填，MP3/WAV，<500MB
  coverImage?: File      // 可選，JPG/PNG，<5MB
}
```

### 🎨 UI/UX 設計
1. **多步驟表單**
   - Step 1: 文件上傳
   - Step 2: Episode 信息
   - Step 3: 發布設置
   - Step 4: 確認和提交

2. **響應式設計**
   - 桌面：三欄布局
   - 平板：兩欄布局
   - 手機：單欄布局

3. **用戶體驗**
   - 拖拽上傳
   - 實時驗證
   - 進度指示器
   - 錯誤處理

## 實作步驟

### Phase 2.1: S3 CRUD 測試 ✅
- [x] 創建 S3 測試 API (`/api/test-s3`)
- [x] 測試文件上傳 (預簽名 URL 生成和上傳)
- [x] 測試文件下載 (預簽名下載 URL)
- [x] 測試文件刪除 (DELETE API)
- [x] 驗證預簽名 URL (成功生成和使用)

### Phase 2.2: 基礎上傳組件 ✅
- [x] AudioUploader 組件 (`components/upload/AudioUploader.tsx`)
- [x] ImageUploader 組件 (`components/upload/ImageUploader.tsx`)
- [x] 文件驗證邏輯 (`lib/upload.ts`)
- [x] 上傳進度顯示 (Progress 組件集成)

### Phase 2.3: Episode 表單 ✅
- [x] EpisodeForm 組件 (集成在 upload 頁面)
- [x] 表單驗證 (`lib/validation.ts`)
- [x] 日期選擇器 (發布日期和過期日期)
- [x] 標籤輸入 (逗號分隔)

### Phase 2.4: 整合和測試 ✅
- [x] 整合所有組件 (完整的 upload 頁面)
- [x] 端到端測試 (S3 上傳 + Supabase 存儲)
- [x] 錯誤處理 (上傳錯誤、驗證錯誤)
- [x] 用戶體驗優化 (加載狀態、進度條)

### Phase 2.5: 增強功能 🔄
- [x] 音頻預覽播放 (AudioUploader 中的播放按鈕)
- [x] 自動提取音頻 metadata (時長、標題)
- [ ] 批量上傳 (未來功能)
- [ ] 草稿自動保存 (未來功能)

## 數據流設計

### 📤 上傳流程
```
1. 前端選擇文件
   ↓
2. 調用 /api/upload/presigned 獲取預簽名 URL
   ↓
3. 直接上傳到 S3
   ↓
4. 獲取 S3 文件 URL
   ↓
5. 提交表單到 /api/episodes
   ↓
6. 存儲 metadata 到 Supabase
   ↓
7. 返回成功響應
```

### 🗄️ 數據存儲
- **S3**: 音頻文件、封面圖片
- **Supabase**: Episode metadata、文件 URL 引用

## 錯誤處理策略

### 🚨 錯誤類型
1. **文件驗證錯誤**
   - 格式不支援
   - 文件過大
   - 文件損壞

2. **上傳錯誤**
   - 網絡中斷
   - S3 權限問題
   - 存儲空間不足

3. **表單驗證錯誤**
   - 必填字段缺失
   - 格式不正確
   - 業務邏輯錯誤

### 🔧 處理機制
- 友好的錯誤消息
- 自動重試機制
- 部分失敗恢復
- 用戶操作指導

## 性能優化

### ⚡ 優化策略
1. **文件上傳**
   - 分片上傳大文件
   - 並行上傳多個文件
   - 上傳進度實時更新

2. **用戶體驗**
   - 預加載組件
   - 懶加載非關鍵資源
   - 骨架屏加載狀態

3. **緩存策略**
   - 預簽名 URL 緩存
   - 表單數據本地存儲
   - 圖片預覽緩存

## 安全考慮

### 🔒 安全措施
1. **文件驗證**
   - MIME 類型檢查
   - 文件頭驗證
   - 病毒掃描 (未來)

2. **權限控制**
   - 預簽名 URL 過期時間
   - 文件大小限制
   - 上傳頻率限制

3. **數據保護**
   - 敏感信息加密
   - 輸入數據清理
   - SQL 注入防護

## 測試計劃

### 🧪 測試類型
1. **單元測試**
   - 文件驗證函數
   - 表單驗證邏輯
   - API 端點

2. **集成測試**
   - S3 上傳流程
   - 數據庫操作
   - 端到端工作流

3. **用戶測試**
   - 不同文件格式
   - 邊界條件
   - 錯誤場景

## 成功指標

### 📊 KPI
- 文件上傳成功率 > 95%
- 平均上傳時間 < 30秒 (10MB 文件)
- 用戶完成率 > 80%
- 錯誤率 < 5%

## 完成總結

### ✅ 已實現功能

#### 🔧 S3 集成
- **測試 API**: `/api/test-s3` 提供完整的 S3 CRUD 測試
- **文件上傳**: 支援預簽名 URL 生成和直接上傳到 S3
- **文件管理**: 支援下載和刪除操作
- **環境配置**: 完整的 S3 配置驗證

#### 📁 文件上傳組件
- **AudioUploader**:
  - 拖拽上傳支援
  - 音頻預覽播放
  - 自動提取 metadata (時長、標題)
  - 進度條顯示
  - 文件驗證 (格式、大小)

- **ImageUploader**:
  - 拖拽上傳支援
  - 圖片預覽
  - 文件驗證
  - 進度條顯示

#### 📋 表單系統
- **完整驗證**: 所有字段的前端驗證
- **錯誤處理**: 友好的錯誤消息顯示
- **自動填充**: 從音頻 metadata 自動填充標題
- **響應式設計**: 適配桌面和移動設備

#### 🔄 工作流程
1. 用戶上傳音頻文件到 S3
2. 自動提取音頻 metadata
3. 用戶填寫 episode 信息
4. 表單驗證
5. 創建 episode 記錄到 Supabase
6. 重定向到 episodes 頁面

### 🎯 技術成就
- **無縫集成**: S3 文件存儲 + Supabase 數據庫
- **用戶體驗**: 直觀的拖拽上傳界面
- **錯誤處理**: 完善的錯誤處理和用戶反饋
- **性能優化**: 直接上傳到 S3，不經過服務器
- **安全性**: 預簽名 URL 確保安全上傳

### 📊 測試結果
- **S3 連接**: ✅ 成功
- **文件上傳**: ✅ 支援 MP3/WAV (最大 500MB) - CORS 已配置
- **圖片上傳**: ✅ 支援 JPG/PNG (最大 5MB) - CORS 已配置
- **表單驗證**: ✅ 完整的前端驗證
- **數據存儲**: ✅ 成功存儲到 Supabase
- **頁面響應**: ✅ 200 狀態碼
- **CORS 配置**: ✅ 已在 AWS S3 Console 手動配置

### ✅ 已解決的問題

#### 問題：CORS 錯誤 - 已解決 ✅
**現象**: 上傳文件時出現 CORS 政策錯誤
```
Access to XMLHttpRequest at 'https://relaxmoment-bill.s3.us-west-2.amazonaws.com/...'
from origin 'http://localhost:3000' has been blocked by CORS policy
```

**解決過程**:
1. ✅ 創建了詳細的 CORS 設置指南：`docs/s3-cors-setup.md`
2. ✅ 提供了 CORS 配置文件：`scripts/s3-cors-config.json`
3. ✅ 創建了自動化腳本：`scripts/setup-s3-cors.sh`
4. ✅ 用戶在 AWS S3 Console 中手動配置了 CORS 政策

**驗證結果**:
```bash
curl -I -X OPTIONS -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: PUT"
"https://relaxmoment-bill.s3.us-west-2.amazonaws.com/"

# 返回：
Access-Control-Allow-Origin: http://localhost:3000 ✅
Access-Control-Allow-Methods: GET, PUT, POST, DELETE, HEAD ✅
Access-Control-Expose-Headers: ETag, x-amz-meta-custom-header ✅
```

**狀態**: 🎉 **完全解決** - 現在可以正常上傳文件到 S3

### ✅ RSS Feed 功能實現

#### 新增功能：自動 RSS Feed 生成
- **RSS 端點**: `/api/podcast-rss` - 自動生成 Apple Podcasts 兼容的 RSS feed
- **Episode 管理**: `/api/episodes/[id]` - 支援 GET/PATCH/DELETE 操作
- **狀態管理**: 可以將 episode 從 draft 改為 published

#### 測試結果
- **RSS 生成**: ✅ 成功生成符合 Apple Podcasts 標準的 RSS XML
- **真實音頻**: ✅ RSS 包含真實的 S3 音頻文件 URL
- **Episode 管理**: ✅ 可以更新 episode 狀態為 published
- **音頻播放**: ✅ 音頻文件可以直接播放

#### 實際測試案例
- **Episode**: "打造完美的 Claude_md 設定"
- **音頻 URL**: `https://relaxmoment-bill.s3.amazonaws.com/episodes/cjymDeoPBB-打造完美的 Claude_md 設定.wav`
- **RSS Feed**: `http://localhost:3000/api/podcast-rss`
- **狀態**: Published ✅

---

**開始日期**: 2025-07-02
**完成日期**: 2025-07-02
**狀態**: ✅ **完成** - Upload 功能和 RSS Feed 已完全實作並測試通過
