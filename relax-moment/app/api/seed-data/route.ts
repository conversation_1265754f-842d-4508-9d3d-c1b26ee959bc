import { NextResponse } from "next/server"
import { supabase, isSupabaseConfigured } from "@/lib/supabase"

export async function POST() {
  try {
    if (!isSupabaseConfigured()) {
      return NextResponse.json({ error: "Supabase not configured" }, { status: 503 })
    }

    // Sample episodes data
    const sampleEpisodes = [
      {
        title: "Welcome to Our Podcast",
        description: "In this inaugural episode, we introduce ourselves and discuss what you can expect from our podcast series.",
        audio_url: "https://example.com/audio/episode-1.mp3",
        duration: "30:00", // 30 minutes
        publish_date: new Date().toISOString(),
        expiry_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        status: "published",
        category: "technology",
        cover_image_url: "https://example.com/images/episode-1-cover.jpg",
        tags: ["introduction", "welcome"]
      },
      {
        title: "Deep Dive into React 19",
        description: "We explore the new features and improvements in React 19, including the new compiler and concurrent features.",
        audio_url: "https://example.com/audio/episode-2.mp3",
        duration: "45:00", // 45 minutes
        publish_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        expiry_date: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000).toISOString(), // 23 days from now
        status: "published",
        category: "technology",
        cover_image_url: "https://example.com/images/episode-2-cover.jpg",
        tags: ["react", "frontend", "development"]
      },
      {
        title: "Building Modern Web Apps",
        description: "A comprehensive guide to building modern web applications with Next.js, TypeScript, and modern tooling.",
        audio_url: "https://example.com/audio/episode-3.mp3",
        duration: "60:00", // 60 minutes
        publish_date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
        expiry_date: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000).toISOString(), // 16 days from now
        status: "published",
        category: "education",
        cover_image_url: "https://example.com/images/episode-3-cover.jpg",
        tags: ["nextjs", "typescript", "web development"]
      }
    ]

    // Insert episodes
    const { data: episodesData, error: episodesError } = await supabase
      .from('episodes')
      .insert(sampleEpisodes)
      .select()

    if (episodesError) {
      return NextResponse.json({ 
        error: "Failed to insert episodes", 
        details: episodesError.message 
      }, { status: 500 })
    }

    // Sample RSS feed data
    const sampleRssFeed = {
      title: "Tech Talk Podcast",
      description: "A podcast about technology, development, and innovation",
      author: "Tech Talk Team",
      email: "<EMAIL>",
      image_url: "https://example.com/images/podcast-cover.jpg",
      language: "en-us",
      category: "Technology",
      subcategory: "Software How-To",
      copyright: "© 2025 Tech Talk Podcast",
      url: "https://example.com/rss/feed.xml",
      is_active: true,
      explicit: false
    }

    // Insert RSS feed
    const { data: rssData, error: rssError } = await supabase
      .from('rss_feeds')
      .insert([sampleRssFeed])
      .select()

    if (rssError) {
      return NextResponse.json({ 
        error: "Failed to insert RSS feed", 
        details: rssError.message 
      }, { status: 500 })
    }

    return NextResponse.json({
      message: "Sample data inserted successfully!",
      episodes: episodesData,
      rss_feed: rssData[0]
    })

  } catch (error) {
    console.error("Seed data error:", error)
    return NextResponse.json({ 
      error: "Unexpected error during data seeding",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
