// File upload utilities and validation

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export interface UploadResult {
  success: boolean
  fileUrl?: string
  key?: string
  error?: string
}

// File validation
export const AUDIO_TYPES = ['audio/mpeg', 'audio/mp3', 'audio/wav']
export const IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png']
export const MAX_AUDIO_SIZE = 500 * 1024 * 1024 // 500MB
export const MAX_IMAGE_SIZE = 5 * 1024 * 1024   // 5MB

export function validateAudioFile(file: File): { valid: boolean; error?: string } {
  if (!AUDIO_TYPES.includes(file.type)) {
    return { valid: false, error: 'Please select an MP3 or WAV audio file' }
  }
  
  if (file.size > MAX_AUDIO_SIZE) {
    return { valid: false, error: 'Audio file must be less than 500MB' }
  }
  
  return { valid: true }
}

export function validateImageFile(file: File): { valid: boolean; error?: string } {
  if (!IMAGE_TYPES.includes(file.type)) {
    return { valid: false, error: 'Please select a JPG or PNG image file' }
  }
  
  if (file.size > MAX_IMAGE_SIZE) {
    return { valid: false, error: 'Image file must be less than 5MB' }
  }
  
  return { valid: true }
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// Get presigned upload URL
export async function getUploadUrl(fileName: string, fileType: string): Promise<{
  uploadUrl: string
  fileKey: string
  publicUrl: string
}> {
  const response = await fetch('/api/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      fileName,
      fileType,
    }),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to get upload URL')
  }

  const data = await response.json()
  return {
    uploadUrl: data.uploadUrl,
    fileKey: data.fileKey,
    publicUrl: data.fileUrl,
  }
}

// Upload file to S3 with progress tracking
export async function uploadFileToS3(
  file: File,
  uploadUrl: string,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> {
  return new Promise((resolve) => {
    const xhr = new XMLHttpRequest()

    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        const progress: UploadProgress = {
          loaded: event.loaded,
          total: event.total,
          percentage: Math.round((event.loaded / event.total) * 100),
        }
        onProgress(progress)
      }
    })

    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        resolve({ success: true })
      } else {
        resolve({ 
          success: false, 
          error: `Upload failed with status ${xhr.status}` 
        })
      }
    })

    xhr.addEventListener('error', () => {
      resolve({ 
        success: false, 
        error: 'Upload failed due to network error' 
      })
    })

    xhr.addEventListener('abort', () => {
      resolve({ 
        success: false, 
        error: 'Upload was cancelled' 
      })
    })

    xhr.open('PUT', uploadUrl)
    xhr.setRequestHeader('Content-Type', file.type)
    xhr.send(file)
  })
}

// Complete upload process
export async function uploadFile(
  file: File,
  onProgress?: (progress: UploadProgress) => void
): Promise<UploadResult> {
  try {
    // Validate file
    const isAudio = AUDIO_TYPES.includes(file.type)
    const isImage = IMAGE_TYPES.includes(file.type)
    
    if (isAudio) {
      const validation = validateAudioFile(file)
      if (!validation.valid) {
        return { success: false, error: validation.error }
      }
    } else if (isImage) {
      const validation = validateImageFile(file)
      if (!validation.valid) {
        return { success: false, error: validation.error }
      }
    } else {
      return { success: false, error: 'Unsupported file type' }
    }

    // Get upload URL
    const { uploadUrl, fileKey, publicUrl } = await getUploadUrl(file.name, file.type)

    // Upload to S3
    const uploadResult = await uploadFileToS3(file, uploadUrl, onProgress)
    
    if (uploadResult.success) {
      return {
        success: true,
        fileUrl: publicUrl,
        key: fileKey,
      }
    } else {
      return uploadResult
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed',
    }
  }
}

// Extract audio metadata (basic implementation)
export function getAudioMetadata(file: File): Promise<{
  duration?: number
  title?: string
}> {
  return new Promise((resolve) => {
    const audio = new Audio()
    const url = URL.createObjectURL(file)
    
    audio.addEventListener('loadedmetadata', () => {
      resolve({
        duration: audio.duration,
        title: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
      })
      URL.revokeObjectURL(url)
    })
    
    audio.addEventListener('error', () => {
      resolve({
        title: file.name.replace(/\.[^/.]+$/, ''),
      })
      URL.revokeObjectURL(url)
    })
    
    audio.src = url
  })
}
