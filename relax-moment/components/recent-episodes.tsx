"use client"

import { useEffect, useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Edit, MoreHorizontal, Trash, Download, Loader2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import Link from "next/link"
import { useToast } from "@/components/ui/use-toast"
import { DeleteDialog } from "@/components/episodes/DeleteDialog"
import { downloadEpisodeAudio } from "@/lib/download"

interface Episode {
  id: string
  title: string
  description: string
  audio_url: string
  cover_image_url: string | null
  publish_date: string
  expiry_date: string | null
  duration: string | null
  status: string
  category: string | null
  tags: string[]
  created_at: string
  updated_at: string
}

export function RecentEpisodes() {
  const { toast } = useToast()
  const [episodes, setEpisodes] = useState<Episode[]>([])
  const [loading, setLoading] = useState(true)

  // Action states
  const [downloadingId, setDownloadingId] = useState<string | null>(null)
  const [deleteEpisode, setDeleteEpisode] = useState<Episode | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  useEffect(() => {
    async function fetchEpisodes() {
      try {
        const response = await fetch('/api/episodes')
        if (response.ok) {
          const data = await response.json()
          // Get only the first 4 episodes for recent episodes
          setEpisodes(data.episodes.slice(0, 4))
        }
      } catch (error) {
        console.error('Failed to fetch episodes:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchEpisodes()
  }, [])

  // Action handlers
  const handleDownload = async (episode: Episode) => {
    setDownloadingId(episode.id)
    try {
      await downloadEpisodeAudio({
        title: episode.title,
        audio_url: episode.audio_url
      })
      toast({
        title: "Download started",
        description: `Downloading "${episode.title}"`,
      })
    } catch (error) {
      console.error('Download failed:', error)
      toast({
        title: "Download failed",
        description: error instanceof Error ? error.message : "Failed to download episode",
        variant: "destructive",
      })
    } finally {
      setDownloadingId(null)
    }
  }

  const handleDeleteClick = (episode: Episode) => {
    setDeleteEpisode(episode)
    setDeleteDialogOpen(true)
  }

  const handleDeleteSuccess = (episodeId: string) => {
    setEpisodes(prev => prev.filter(ep => ep.id !== episodeId))
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString()
  }

  function getStatusVariant(status: string) {
    switch (status) {
      case "published":
        return "default"
      case "expiring-soon":
        return "outline" as const
      case "expired":
        return "destructive" as const
      default:
        return "secondary" as const
    }
  }

  function getStatusLabel(status: string) {
    switch (status) {
      case "published":
        return "Published"
      case "expiring-soon":
        return "Expiring Soon"
      case "expired":
        return "Expired"
      case "draft":
        return "Draft"
      default:
        return status
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-md border">
          <div className="relative w-full overflow-auto">
            <table className="w-full caption-bottom text-sm">
              <thead className="[&_tr]:border-b">
                <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Title</th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground hidden md:table-cell">
                    Published
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground hidden md:table-cell">
                    Expires
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground hidden md:table-cell">
                    Duration
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Status</th>
                  <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">Actions</th>
                </tr>
              </thead>
              <tbody className="[&_tr:last-child]:border-0">
                {[...Array(4)].map((_, i) => (
                  <tr key={i} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                    <td className="p-4 align-middle">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-9 w-9">
                          <AvatarFallback>--</AvatarFallback>
                        </Avatar>
                        <div className="font-medium">Loading...</div>
                      </div>
                    </td>
                    <td className="p-4 align-middle hidden md:table-cell">--</td>
                    <td className="p-4 align-middle hidden md:table-cell">--</td>
                    <td className="p-4 align-middle hidden md:table-cell">--</td>
                    <td className="p-4 align-middle">
                      <Badge variant="secondary">Loading</Badge>
                    </td>
                    <td className="p-4 align-middle text-right">
                      <Button variant="ghost" size="icon" disabled>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <div className="relative w-full overflow-auto">
          <table className="w-full caption-bottom text-sm">
            <thead className="[&_tr]:border-b">
              <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Title</th>
                <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground hidden md:table-cell">
                  Published
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground hidden md:table-cell">
                  Expires
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground hidden md:table-cell">
                  Duration
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Status</th>
                <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">Actions</th>
              </tr>
            </thead>
            <tbody className="[&_tr:last-child]:border-0">
              {episodes.map((episode) => (
                <tr
                  key={episode.id}
                  className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
                >
                  <td className="p-4 align-middle">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-9 w-9">
                        <AvatarImage src={episode.cover_image_url || undefined} alt={episode.title} />
                        <AvatarFallback>EP</AvatarFallback>
                      </Avatar>
                      <div className="font-medium">{episode.title}</div>
                    </div>
                  </td>
                  <td className="p-4 align-middle hidden md:table-cell">{formatDate(episode.publish_date)}</td>
                  <td className="p-4 align-middle hidden md:table-cell">
                    {episode.expiry_date ? formatDate(episode.expiry_date) : 'No expiry'}
                  </td>
                  <td className="p-4 align-middle hidden md:table-cell">{episode.duration || 'Unknown'}</td>
                  <td className="p-4 align-middle">
                    <Badge variant={getStatusVariant(episode.status)}>
                      {getStatusLabel(episode.status)}
                    </Badge>
                  </td>
                  <td className="p-4 align-middle text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem asChild>
                          <Link href={`/episodes/${episode.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDownload(episode)}
                          disabled={downloadingId === episode.id}
                        >
                          {downloadingId === episode.id ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <Download className="mr-2 h-4 w-4" />
                          )}
                          {downloadingId === episode.id ? 'Downloading...' : 'Download'}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => handleDeleteClick(episode)}
                        >
                          <Trash className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      <div className="flex justify-end">
        <Link href="/episodes">
          <Button variant="outline" size="sm">
            View All Episodes
          </Button>
        </Link>
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteDialog
        episode={deleteEpisode}
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onDeleteSuccess={handleDeleteSuccess}
      />
    </div>
  )
}
