import { NextRequest, NextResponse } from "next/server"
import { 
  processImageForApplePodcasts, 
  validateImageFile, 
  getImageMetadata,
  needsProcessing,
  getProcessedFilename
} from "@/lib/image-processing"

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 })
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Validate the image
    const validation = await validateImageFile(buffer)
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: 400 })
    }

    const originalMetadata = validation.metadata!

    // Check if processing is needed
    const requiresProcessing = needsProcessing(originalMetadata.width, originalMetadata.height)

    if (!requiresProcessing) {
      // Image already meets requirements, return original
      return NextResponse.json({
        processed: false,
        message: "Image already meets Apple Podcasts requirements",
        original: {
          width: originalMetadata.width,
          height: originalMetadata.height,
          format: originalMetadata.format,
          size: originalMetadata.size
        }
      })
    }

    // Process the image
    const processedResult = await processImageForApplePodcasts(buffer, {
      targetSize: 1400,
      quality: 90,
      format: 'jpeg'
    })

    // Create processed filename
    const processedFilename = getProcessedFilename(file.name, 'jpeg')

    // Return processed image as blob
    const response = new NextResponse(processedResult.buffer, {
      headers: {
        'Content-Type': 'image/jpeg',
        'Content-Disposition': `attachment; filename="${processedFilename}"`,
        'X-Processed': 'true',
        'X-Original-Width': originalMetadata.width.toString(),
        'X-Original-Height': originalMetadata.height.toString(),
        'X-Processed-Width': processedResult.width.toString(),
        'X-Processed-Height': processedResult.height.toString(),
        'X-Original-Size': originalMetadata.size.toString(),
        'X-Processed-Size': processedResult.size.toString(),
        'X-Processed-Filename': processedFilename
      }
    })

    return response

  } catch (error) {
    console.error("Image processing error:", error)
    return NextResponse.json({ 
      error: "Image processing failed",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
