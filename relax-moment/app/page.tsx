import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Upload, ListMusic, Clock, Rss } from "lucide-react"
import Link from "next/link"
import { DashboardStats } from "@/components/dashboard-stats"
import { RecentEpisodes } from "@/components/recent-episodes"

export default function Dashboard() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">Manage your podcast episodes and RSS feeds</p>
        </div>
        <div className="flex gap-2">
          <Link href="/upload">
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              Upload Episode
            </Button>
          </Link>
        </div>
      </div>

      <DashboardStats />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2">
              <Link href="/upload">
                <Button variant="outline" className="w-full justify-start bg-transparent">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload New Episode
                </Button>
              </Link>
              <Link href="/episodes">
                <Button variant="outline" className="w-full justify-start bg-transparent">
                  <ListMusic className="mr-2 h-4 w-4" />
                  Manage Episodes
                </Button>
              </Link>
              <Link href="/episodes/expiring">
                <Button variant="outline" className="w-full justify-start bg-transparent">
                  <Clock className="mr-2 h-4 w-4" />
                  View Expiring Episodes
                </Button>
              </Link>
              <Link href="/rss">
                <Button variant="outline" className="w-full justify-start bg-transparent">
                  <Rss className="mr-2 h-4 w-4" />
                  Manage RSS Feeds
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Recent Episodes</CardTitle>
            <CardDescription>Your recently uploaded podcast episodes</CardDescription>
          </CardHeader>
          <CardContent>
            <RecentEpisodes />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
