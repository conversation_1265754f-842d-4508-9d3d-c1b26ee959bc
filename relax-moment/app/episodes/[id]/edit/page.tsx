"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { CalendarIcon, Save, ArrowLeft, AlertCircle } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { validateEpisodeForm, formatT<PERSON><PERSON>, tagsToString, type EpisodeFormD<PERSON>, type ValidationError } from "@/lib/validation"
import Link from "next/link"

interface Episode {
  id: string
  title: string
  description: string
  audio_url: string
  cover_image_url: string | null
  publish_date: string
  expiry_date: string | null
  duration: string | null
  status: string
  category: string | null
  tags: string[]
  created_at: string
  updated_at: string
}

export default function EditEpisodePage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const episodeId = params.id as string

  // Form state
  const [formData, setFormData] = useState<EpisodeFormData>({
    title: '',
    description: '',
    category: '',
    tags: [],
    publishDate: new Date(),
    status: 'draft'
  })

  // UI state
  const [loading, setLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<ValidationError[]>([])
  const [episode, setEpisode] = useState<Episode | null>(null)

  useEffect(() => {
    async function fetchEpisode() {
      try {
        const response = await fetch(`/api/episodes/${episodeId}`)
        if (response.ok) {
          const data = await response.json()
          const episodeData = data.episode
          setEpisode(episodeData)
          
          // Populate form with existing data
          setFormData({
            title: episodeData.title || '',
            description: episodeData.description || '',
            category: episodeData.category || '',
            tags: episodeData.tags || [],
            publishDate: new Date(episodeData.publish_date),
            expiryDate: episodeData.expiry_date ? new Date(episodeData.expiry_date) : undefined,
            status: episodeData.status || 'draft'
          })
        } else {
          toast({
            title: "Episode not found",
            description: "The episode you're trying to edit doesn't exist.",
            variant: "destructive",
          })
          router.push('/episodes')
        }
      } catch (error) {
        console.error('Failed to fetch episode:', error)
        toast({
          title: "Failed to load episode",
          description: "An error occurred while loading the episode data.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    if (episodeId) {
      fetchEpisode()
    }
  }, [episodeId, router, toast])

  const handleInputChange = (field: keyof EpisodeFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear field-specific errors when user starts typing
    setErrors(prev => prev.filter(error => error.field !== field))
  }

  const handleTagsChange = (tagsString: string) => {
    const tags = formatTags(tagsString)
    handleInputChange('tags', tags)
  }

  const handleSubmit = async () => {
    // Validate form (excluding audio_url since it's not editable)
    const dataToValidate = {
      ...formData,
      audioUrl: episode?.audio_url || '', // Use existing audio URL
      coverImageUrl: episode?.cover_image_url || ''
    }

    const validation = validateEpisodeForm(dataToValidate)

    if (!validation.valid) {
      setErrors(validation.errors)
      toast({
        title: "Validation failed",
        description: "Please fix the errors below and try again.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const updateData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        tags: formData.tags,
        publish_date: formData.publishDate.toISOString(),
        expiry_date: formData.expiryDate?.toISOString() || null,
        status: formData.status
      }

      const response = await fetch(`/api/episodes/${episodeId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update episode')
      }

      toast({
        title: "Episode updated successfully!",
        description: "Your changes have been saved.",
      })

      // Redirect back to episodes page
      router.push('/episodes')

    } catch (error) {
      console.error('Update error:', error)
      toast({
        title: "Failed to update episode",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getFieldError = (field: string): string | undefined => {
    const error = errors.find(e => e.field === field)
    return error?.message
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/episodes">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loading...</h1>
            <p className="text-muted-foreground">Loading episode data</p>
          </div>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!episode) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/episodes">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Episode Not Found</h1>
            <p className="text-muted-foreground">The episode you're looking for doesn't exist</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/episodes">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Episode</h1>
          <p className="text-muted-foreground">Update episode information and settings</p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Episode Information */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Episode Information</CardTitle>
              <CardDescription>
                Update details about your podcast episode
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter episode title"
                  disabled={isSubmitting}
                />
                {getFieldError('title') && (
                  <p className="text-sm text-destructive">{getFieldError('title')}</p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe your episode content..."
                  rows={4}
                  disabled={isSubmitting}
                />
                {getFieldError('description') && (
                  <p className="text-sm text-destructive">{getFieldError('description')}</p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => handleInputChange('category', value)}
                  disabled={isSubmitting}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="technology">Technology</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="education">Education</SelectItem>
                    <SelectItem value="entertainment">Entertainment</SelectItem>
                    <SelectItem value="health">Health & Fitness</SelectItem>
                    <SelectItem value="news">News & Politics</SelectItem>
                  </SelectContent>
                </Select>
                {getFieldError('category') && (
                  <p className="text-sm text-destructive">{getFieldError('category')}</p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  value={tagsToString(formData.tags)}
                  onChange={(e) => handleTagsChange(e.target.value)}
                  placeholder="Enter tags separated by commas"
                  disabled={isSubmitting}
                />
                <p className="text-xs text-muted-foreground">
                  Separate tags with commas (max 10 tags)
                </p>
                {getFieldError('tags') && (
                  <p className="text-sm text-destructive">{getFieldError('tags')}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Publishing Settings & File Info */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Publishing Settings</CardTitle>
              <CardDescription>
                Configure when and how your episode will be published
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="publish-date">Publish Date *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        id="publish-date"
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.publishDate && "text-muted-foreground"
                        )}
                        disabled={isSubmitting}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.publishDate ? format(formData.publishDate, "PPP") : "Select date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.publishDate}
                        onSelect={(date) => date && handleInputChange('publishDate', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  {getFieldError('publishDate') && (
                    <p className="text-sm text-destructive">{getFieldError('publishDate')}</p>
                  )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="expiry-date">Expiry Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        id="expiry-date"
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.expiryDate && "text-muted-foreground"
                        )}
                        disabled={isSubmitting}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.expiryDate ? format(formData.expiryDate, "PPP") : "Select date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.expiryDate}
                        onSelect={(date) => handleInputChange('expiryDate', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  {getFieldError('expiryDate') && (
                    <p className="text-sm text-destructive">{getFieldError('expiryDate')}</p>
                  )}
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: 'draft' | 'published') => handleInputChange('status', value)}
                  disabled={isSubmitting}
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                  </SelectContent>
                </Select>
                {getFieldError('status') && (
                  <p className="text-sm text-destructive">{getFieldError('status')}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* File Information (Read-only) */}
          <Card>
            <CardHeader>
              <CardTitle>File Information</CardTitle>
              <CardDescription>
                Current audio and image files (cannot be changed)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label>Audio File</Label>
                <div className="p-3 bg-muted rounded-md">
                  <p className="text-sm font-medium">Duration: {episode.duration || 'Unknown'}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    To change the audio file, create a new episode
                  </p>
                </div>
              </div>
              
              {episode.cover_image_url && (
                <div className="grid gap-2">
                  <Label>Cover Image</Label>
                  <div className="p-3 bg-muted rounded-md">
                    <img 
                      src={episode.cover_image_url} 
                      alt="Cover" 
                      className="w-16 h-16 object-cover rounded"
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Link href="/episodes">
          <Button
            variant="outline"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        </Link>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="min-w-[120px]"
        >
          {isSubmitting ? (
            <>
              <Save className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
